<?xml version="1.0" encoding="utf-8"?>
<config>
    <popup id="AUT90902" title="驛ｨ髢讀懃ｴ｢" width="800" height="600" init="1" page="0"
        description="驛ｨ髢讀懃ｴ｢">
        <panel width="300" cols="2">
            <input id="param0" type="hidden" value="param0" order="1" source=""></input>
            <message id="" value="驛ｨ髢蜷咲ｧｰ" order="2" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="bunrui1_nm" type="text" size="20" maxlength="20" order="3" value="bunrui1_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="4" value="驛ｨ髢蜷咲ｧｰ縺ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="5" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="bunrui1_cd" mapping="BUNRUI1_CD" label="驛ｨ髢繧ｳ繝ｼ繝�" order="1" width="30%"
                style="height:28;text-align:left;" />
            <column name="bunrui1_kanji_na" mapping="BUNRUI1_KANJI_NA" label="驛ｨ髢蜷咲ｧｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>Bunrui1SearchQuery</id>
            <body>
                SELECT DISTINCT
                    RB1.BUNRUI1_CD,
                    RB1.BUNRUI1_KANJI_NA
                FROM
                    R_BUNRUI1 RB1
                    INNER JOIN R_BUNRUI2 RB2
                        ON RB2.BUNRUI1_CD = RB1.BUNRUI1_CD
                    INNER JOIN R_BUNRUI3 RB3
                        ON RB3.BUNRUI2_CD = RB2.BUNRUI2_CD
            </body>
            <where order="1" bind="param0">RB1.SYSTEM_KB=:param0</where>
            <where order="2" bind="bunrui1_nm">RB1.BUNRUI1_KANJI_NA LIKE %:bunrui1_nm%</where>
            <orderby>RB1.BUNRUI1_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90903" title="螟ｧ蛻�鬘樊､懃ｴ｢" width="800" height="600" init="1" page="0"
        description="螟ｧ蛻�鬘樊､懃ｴ｢">
        <panel width="520" cols="2">
            <input id="param0" type="hidden" value="param0" order="1" source=""></input>
            <message id="" value="* 驛ｨ髢繧ｳ繝ｼ繝�" order="2" style="margin:5px 10px 0px 0px;float:left;" />
            <input id="param1" type="text" size="10" maxlength="2" order="3" value="param1"
                style="float:left;background-color:#e0e0e0;color:#383838;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="2" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ2譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="螟ｧ蛻�鬘槫錐遘ｰ" order="4" style="margin:5px 8px 0px 100px;float:left;" />
            <input id="bunrui2_nm" type="text" size="20" maxlength="20" order="5" value="bunrui2_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="6" value="螟ｧ蛻�鬘槫錐遘ｰ縺ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="7" value="讀懃ｴ｢" script="doSearch()"
                style="margin:2px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="bunrui2_cd" mapping="BUNRUI2_CD" label="螟ｧ蛻�鬘槭さ繝ｼ繝�" order="1" width="30%"
                style="height:28;text-align:left;" />
            <column name="bunrui2_kanji_na" mapping="BUNRUI2_KANJI_NA" label="螟ｧ蛻�鬘槫錐遘ｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>Bunrui2SearchQuery</id>
            <body>
                SELECT DISTINCT
                    RB2.BUNRUI2_CD,
                    RB2.BUNRUI2_KANJI_NA
                FROM
                    R_BUNRUI2 RB2
                    INNER JOIN R_BUNRUI3 RB3
                        ON RB3.BUNRUI2_CD = RB2.BUNRUI2_CD
            </body>
            <where order="1" bind="param0">RB2.SYSTEM_KB=:param0</where>
            <!-- 2020.02.25 N.Hanai MOD(S) SQLServer蛹門ｯｾ蠢� -->
<!--             <where order="2" bind="param1">TRIM(RB2.BUNRUI1_CD)=:param1</where> -->
            <where order="2" bind="param1">RTRIM(RB2.BUNRUI1_CD)=:param1</where>
            <!-- 2020.02.25 N.Hanai MOD(E) SQLServer蛹門ｯｾ蠢� -->
            <where order="3" bind="bunrui2_nm">RB2.BUNRUI2_KANJI_NA LIKE %:bunrui2_nm%</where>
            <orderby>RB2.BUNRUI2_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90904" title="荳ｭ蛻�鬘樊､懃ｴ｢" width="800" height="600" init="1" page="0"
        description="荳ｭ蛻�鬘樊､懃ｴ｢">
        <panel width="520" cols="2">
            <input id="param0" type="hidden" value="param0" order="1" source=""></input>
            <message id="" value="* 驛ｨ髢繧ｳ繝ｼ繝�" order="2" style="margin:5px 31px 0px 0px;float:left;" />
            <input id="param1" type="text" size="10" maxlength="2" order="3" value="param1"
                style="float:left;background-color:#e0e0e0;color:#383838;float:left;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="2" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ2譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="* 螟ｧ蛻�鬘槭さ繝ｼ繝�" order="4" style="margin:5px 8px 0px 100px;float:left;" />
            <input id="param2" type="text" size="10" maxlength="4" order="5" value="param2" style="background-color:#e0e0e0;color:#383838;"
                readonly="true">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="荳ｭ蛻�鬘槫錐遘ｰ" order="6" style="margin:5px 35px 0px 0px;float:left;" />
            <input id="bunrui3_nm" type="text" size="20" maxlength="20" order="7" value="bunrui3_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="8" value="荳ｭ蛻�鬘槫錐遘ｰ縺ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="9" value="讀懃ｴ｢" script="doSearch()"
                style="margin:2px 5px 6px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="bunrui3_cd" mapping="BUNRUI3_CD" label="荳ｭ蛻�鬘槭さ繝ｼ繝�" order="1" width="30%"
                style="height:28;text-align:left;" />
            <column name="bunrui3_kanji_na" mapping="BUNRUI3_KANJI_NA" label="荳ｭ蛻�鬘槫錐遘ｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>Bunrui3SearchQuery</id>
            <body>
                SELECT DISTINCT
                    RB3.BUNRUI3_CD,
                    RB3.BUNRUI3_KANJI_NA
                FROM
                    R_BUNRUI3 RB3
                    INNER JOIN R_BUNRUI2 RB2
                        ON RB2.BUNRUI2_CD = RB3.BUNRUI2_CD
            </body>
            <where order="1" bind="param0">RB3.SYSTEM_KB=:param0</where>
            <!-- 2020.02.25 N.Hanai MOD(S) SQLServer蛹門ｯｾ蠢� -->
<!--             <where order="2" bind="param1">TRIM(RB2.BUNRUI1_CD) = :param1</where> -->
<!--             <where order="3" bind="param2">TRIM(RB3.BUNRUI2_CD) = :param2</where> -->
            <where order="2" bind="param1">RTRIM(RB2.BUNRUI1_CD) = :param1</where>
            <where order="3" bind="param2">RTRIM(RB3.BUNRUI2_CD) = :param2</where>
            <!-- 2020.02.25 N.Hanai MOD(E) SQLServer蛹門ｯｾ蠢� -->
            <where order="4" bind="bunrui3_nm">RB3.BUNRUI3_KANJI_NA LIKE %:bunrui3_nm%</where>
            <orderby>RB3.BUNRUI3_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90905" title="蟆丞��鬘樊､懃ｴ｢" width="800" height="600" init="1" page="0"
        description="蟆丞��鬘樊､懃ｴ｢">
        <panel width="520" cols="2">
            <input id="param0" type="hidden" value="param0" order="1" source=""></input>
            <message id="" value="* 驛ｨ髢繧ｳ繝ｼ繝�" order="2" style="margin:5px 35px 0px 0px;float:left;" />
            <input id="param1" type="text" size="10" maxlength="2" order="3" value="param1"
                style="float:left;background-color:#e0e0e0;color:#383838;float:left;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="2" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ2譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="* 螟ｧ蛻�鬘槭さ繝ｼ繝�" order="4" style="margin:5px 15px 0px 100px;float:left;" />
            <input id="param2" type="text" size="10" maxlength="4" order="5" value="param2" style="background-color:#e0e0e0;color:#383838;"
                readonly="true">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="* 荳ｭ蛻�鬘槭さ繝ｼ繝�" order="6" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="param3" type="text" size="10" maxlength="6" order="7" value="param3" style="background-color:#e0e0e0;color:#383838;"
                readonly="true">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="6" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ6譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="蟆丞��鬘槫錐遘ｰ" order="8" style="margin:5px 38px 0px 0px;float:left;" />
            <input id="bunrui4_nm" type="text" size="20" maxlength="20" order="9" value="bunrui4_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="10" value="蟆丞��鬘槫錐遘ｰ縺ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="11" value="讀懃ｴ｢" script="doSearch()"
                style="margin:2px 5px 6px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="bunrui4_cd" mapping="BUNRUI4_CD" label="蟆丞��鬘槭さ繝ｼ繝�" order="1" width="30%"
                style="height:28;text-align:left;" />
            <column name="bunrui4_kanji_na" mapping="BUNRUI4_KANJI_NA" label="蟆丞��鬘槫錐遘ｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>Bunrui4SearchQuery</id>
            <body>
                SELECT DISTINCT
                    RB4.BUNRUI4_CD,
                    RB4.BUNRUI4_KANJI_NA
                FROM
                    R_BUNRUI4 RB4
                    INNER JOIN R_BUNRUI3 RB3
                        ON RB3.BUNRUI3_CD = RB4.BUNRUI3_CD
                    INNER JOIN R_BUNRUI2 RB2
                        ON RB2.BUNRUI2_CD = RB3.BUNRUI2_CD
            </body>
            <where order="1" bind="param0">RB4.SYSTEM_KB=:param0</where>
            <!-- 2020.02.25 N.Hanai MOD(S) SQLServer蛹門ｯｾ蠢� -->
<!--             <where order="2" bind="param1">TRIM(RB2.BUNRUI1_CD)=:param1</where> -->
<!--             <where order="3" bind="param2">TRIM(RB3.BUNRUI2_CD)=:param2</where> -->
<!--             <where order="4" bind="param3">TRIM(RB4.BUNRUI3_CD)=:param3</where> -->
            <where order="2" bind="param1">RTRIM(RB2.BUNRUI1_CD)=:param1</where>
            <where order="3" bind="param2">RTRIM(RB3.BUNRUI2_CD)=:param2</where>
            <where order="4" bind="param3">RTRIM(RB4.BUNRUI3_CD)=:param3</where>
            <!-- 2020.02.25 N.Hanai MOD(E) SQLServer蛹門ｯｾ蠢� -->
            <where order="5" bind="bunrui4_nm">RB4.BUNRUI4_KANJI_NA LIKE %:bunrui4_nm%</where>
            <orderby>RB4.BUNRUI4_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90906" title="蛻�鬘橸ｼ墓､懃ｴ｢" width="800" height="600" init="1" page="0"
        description="蛻�鬘橸ｼ墓､懃ｴ｢">
        <panel width="520" cols="2">
            <input id="param0" type="hidden" value="param0" order="1" source=""></input>
            <message id="" value="* 驛ｨ髢繧ｳ繝ｼ繝�" order="1" style="margin:5px 31px 0px 0px;float:left;" />
            <input id="param1" type="text" size="10" maxlength="2" order="2" value="param1"
                style="float:left; background-color:#e0e0e0; color:#383838;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="2" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ2譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="* 螟ｧ蛻�鬘槭さ繝ｼ繝�" order="3" style="margin:5px 8px 0px 100px;float:left;" />
            <input id="param2" type="text" size="10" maxlength="4" order="4" value="param2"
                style="background-color:#e0e0e0; color:#383838;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="* 荳ｭ蛻�鬘槭さ繝ｼ繝�" order="5" style="margin:5px 31px 0px 0px;float:left;" />
            <input id="param3" type="text" size="10" maxlength="6" order="6" value="param3"
                style="float:left; background-color:#e0e0e0; color:#383838;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="6" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ6譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="* 蟆丞��鬘槭さ繝ｼ繝�" order="7" style="margin:5px 8px 0px 100px;float:left;" />
            <input id="param4" type="text" size="10" maxlength="8" order="8" value="param4"
                style="background-color:#e0e0e0; color:#383838;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="8" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ8譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="蛻�鬘橸ｼ募錐遘ｰ" order="9" style="margin:5px 31px 0px 0px;float:left;" />
            <input id="bunrui5_nm" type="text" size="20" maxlength="20" order="10" value="bunrui5_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="12" value="蛻�鬘橸ｼ募錐遘ｰ縺ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="13" value="讀懃ｴ｢" script="doSearch()"
                style="margin:2px 5px 6px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="bunrui5_cd" mapping="BUNRUI5_CD" label="蛻�鬘橸ｼ輔さ繝ｼ繝�" order="1" width="30%"
                style="text-align:center;" />
            <column name="bunrui5_kanji_na" mapping="BUNRUI5_KANJI_NA" label="蛻�鬘橸ｼ募錐遘ｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>Bunrui5SearchQuery</id>
            <body>
                SELECT DISTINCT
                    RB5.BUNRUI5_CD,
                    RB5.BUNRUI5_KANJI_NA
                FROM
                    R_BUNRUI5 RB5
                    INNER JOIN R_BUNRUI4 RB4
                        ON RB4.BUNRUI4_CD = RB5.BUNRUI4_CD
                    INNER JOIN R_BUNRUI3 RB3
                        ON RB3.BUNRUI3_CD = RB4.BUNRUI3_CD
                    INNER JOIN R_BUNRUI2 RB2
                        ON RB2.BUNRUI2_CD = RB3.BUNRUI2_CD
            </body>
            <where order="1" bind="param0">RB5.SYSTEM_KB=:param0</where>
            <!-- 2020.02.25 N.Hanai MOD(S) SQLServer蛹門ｯｾ蠢� -->
<!--             <where order="2" bind="param1">TRIM(RB2.BUNRUI1_CD)=:param1</where> -->
<!--             <where order="3" bind="param2">TRIM(RB3.BUNRUI2_CD)=:param2</where> -->
<!--             <where order="4" bind="param3">TRIM(RB4.BUNRUI3_CD)=:param3</where> -->
<!--             <where order="5" bind="param4">TRIM(RB5.BUNRUI4_CD)=:param4</where> -->
            <where order="2" bind="param1">RTRIM(RB2.BUNRUI1_CD)=:param1</where>
            <where order="3" bind="param2">RTRIM(RB3.BUNRUI2_CD)=:param2</where>
            <where order="4" bind="param3">RTRIM(RB4.BUNRUI3_CD)=:param3</where>
            <where order="5" bind="param4">RTRIM(RB5.BUNRUI4_CD)=:param4</where>
            <!-- 2020.02.25 N.Hanai MOD(E) SQLServer蛹門ｯｾ蠢� -->
            <where order="6" bind="bunrui5_nm">RB5.BUNRUI5_KANJI_NA LIKE %:bunrui5_nm%</where>
            <orderby>RB5.BUNRUI5_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90907" title="蝠�蜩∵､懃ｴ｢" width="800" height="725" init="0" page="100" description="蝠�蜩∵､懃ｴ｢(蛻�鬘�1謖�螳�)">
        <panel width="520" cols="2">
            <message id="" value="* 驛ｨ髢繧ｳ繝ｼ繝�" order="1" style="margin:5px 23px 0px 0px;float:left;" script="onblur='bunruiOnblur(this,1)'"/>
            <input id="param0" type="text" size="10" maxlength="2" order="2" value="param0"
                style="float:left;background-color:#e0e0e0;color:#383838;float:left;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="2" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ2譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="螟ｧ蛻�鬘槭さ繝ｼ繝�" order="3" style="margin:5px 5px 0px 90px;float:left;" />
            <input id="param1" type="text" size="10" maxlength="4" order="4" value="param1" style="ime-mode:disabled;" script="onblur='bunruiOnblur(this,2)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="荳ｭ蛻�鬘槭さ繝ｼ繝�" order="5" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="param2" type="text" size="10" maxlength="6" order="6" value="param2" style="ime-mode:disabled;float:left;" script="onblur='bunruiOnblur(this,3)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="6" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ6譁�蟄励〒縺吶�" />
            </input>
            <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (S) -->
            <message id="" value="蟆丞��鬘槭さ繝ｼ繝�" order="7" style="margin:5px 7px 0px 88px;float:left;" />
            <input id="param3" type="text" size="10" maxlength="9" order="8" value="param3" style="ime-mode:disabled;" script="onblur='bunruiOnblur(this,4)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="9" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ9譁�蟄励〒縺吶�" />
            </input>
            <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (E) -->
            <message order="9"  id=""              value="蝠�蜩√さ繝ｼ繝�" style="margin:5px 35px 0px 0px;float:left;" />
            <input   order="10"  id="syohin_cd"     value="syohin_cd" type="text" size="17" maxlength="13" style="ime-mode:disabled;float:left;" script="onblur='syohinOnblur(this);'">
                <validate id="isHalfCharacter" value=""   alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength"       value="13" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ13譁�蟄励〒縺吶�" />
            </input>
            <message order="11"  id=""              value="蝠�蜩∝錐遘ｰ" style="margin:5px 25px 0px 55px;float:left;" />
            <input   order="12"  id="syohin_nm"     value="syohin_nm" type="text" size="20" maxlength="20">
                <validate id="maxLength"       value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message order="13"  id="notice1"       value="蝠�蜩√さ繝ｼ繝峨∪縺溘�ｯ蝠�蜩∝錐遘ｰ縺ｮ荳驛ｨ繧貞�･蜉帙＠縺ｦ讀懃ｴ｢繝懊ち繝ｳ繧呈款縺励※縺上□縺輔＞縲�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="14"  id="notice2"       value="蝠�蜩√さ繝ｼ繝峨∝膚蜩∝錐遘ｰ繧貞�･蜉帙＠縺溷�ｴ蜷医�ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="15"  id="notice3"       value="蝠�蜩∵焚縺悟､壹＞蝣ｴ蜷医∵､懃ｴ｢蜿翫�ｳ謾ｹ繝壹�ｼ繧ｸ縺ｫ譎る俣縺後°縺九ｋ縺薙→縺後≠繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <input   order="16"  id="search_button" value="讀懃ｴ｢" type="button" script="doSearch()" style="margin:6px 5px 5px 5px;text-align:center;"></input>
            <!-- 2021/06/10 T.Kaita Redmine#3016蟇ｾ蠢� (S) -->
            <input   order="17"  id="param5"        value="param5" style="display: none;" />
            <!-- 2021/06/10 T.Kaita Redmine#3016蟇ｾ蠢� (E) -->
        </panel>
        <list width="450" cols="3">
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (S) -->
            <!-- <column order="1" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="height:28;text-align:center;" /> -->
            <column order="1" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="height:28;text-align:left;" />
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (E) -->
            <column order="2" name="hinmei_kanji_na" mapping="HINMEI_KANJI_NA" label="蝠�蜩∝錐遘ｰ" width="55%" style="text-align:left;padding-left:5px;" />
            <column order="3" name="Select"          label="" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>SyohinSearchQuery</id>
              <body>SELECT SYOHIN_CD, TRIM(CONCAT(KIKAKU_KANJI_2_NA, ' ', SYOHIN_KANJI_NA, ' ', KIKAKU_KANJI_NA)) HINMEI_KANJI_NA FROM R_SYOHIN</body>
              <where order="1" bind="param0">TRIM(BUNRUI1_CD)=:param0</where>
              <where order="2" bind="param1">TRIM(BUNRUI2_CD)=:param1</where>
              <where order="3" bind="param2">TRIM(BUNRUI3_CD)=:param2</where>
              <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (S) -->
              <where order="4" bind="param3">TRIM(BUNRUI4_CD)=:param3</where>
              <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (E) -->
              <where order="5" bind="syohin_cd">SYOHIN_CD LIKE %:syohin_cd%</where>
              <where order="6" bind="syohin_nm">SYOHIN_KANJI_NA LIKE %:syohin_nm%</where>
              <!-- 2021/06/10 T.Kaita Redmine#3016蟇ｾ蠢� (S) -->
              <where order="7" bind="param5">SHIIRE_HANBAI_KB=:param5</where>
              <!-- 2021/06/10 T.Kaita Redmine#3016蟇ｾ蠢� (E) -->
            <orderby>SYOHIN_CD ASC</orderby>
        </sql>
    </popup>
    <!-- 2022/08/19 T.Eitoku Redmine#6554蟇ｾ蠢�(S) -->
    <!-- <popup id="AUT90926" title="蝠�蜩∬､�謨ｰ讀懃ｴ｢" width="800" height="725" init="0" page="100" description="蝠�蜩√さ繝ｼ繝芽､�謨ｰ驕ｸ謚槫庄"> -->
    <popup id="AUT90926" title="蝠�蜩∬､�謨ｰ讀懃ｴ｢" width="800" height="745" init="0" page="100" description="蝠�蜩√さ繝ｼ繝芽､�謨ｰ驕ｸ謚槫庄">
    <!-- 2022/08/19 T.Eitoku Redmine#6554蟇ｾ蠢�(E) -->
        <panel width="520" cols="2">
            <message id="" value="驛ｨ髢繧ｳ繝ｼ繝�" order="1" style="margin:5px 36px 0px 0px;float:left;" />
            <input id="param0" type="text" size="10" maxlength="2" order="2" value="param0" style="ime-mode:disabled;float:left;" script="onblur='bunruiOnblur(this,1)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="螟ｧ蛻�鬘槭さ繝ｼ繝�" order="3" style="margin:5px 5px 0px 90px;float:left;" />
            <input id="param1" type="text" size="10" maxlength="4" order="4" value="param1" style="ime-mode:disabled;" script="onblur='bunruiOnblur(this,2)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="荳ｭ蛻�鬘槭さ繝ｼ繝�" order="5" style="margin:5px 33px 0px 0px;float:left;" />
            <input id="param2" type="text" size="10" maxlength="6" order="6" value="param2" style="ime-mode:disabled;" script="onblur='bunruiOnblur(this,3)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="6" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ6譁�蟄励〒縺吶�" />
            </input>
            <message order="9"  id=""  value="蝠�蜩√さ繝ｼ繝�" style="margin:5px 46px 0px 0px;float:left;" />
            <input   order="10"  id="syohin_cd"     value="syohin_cd" type="text" size="17" maxlength="14" style="ime-mode:disabled;float:left;" script="onblur='syohinOnblur(this);'">
                <validate id="isHalfCharacter" value=""   alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength"       value="13" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ14譁�蟄励〒縺吶�" />
            </input>
            <message order="11"  id=""              value="蝠�蜩∝錐遘ｰ" style="margin:5px 7px 0px 71px;float:left;" />
            <input   order="12"  id="syohin_nm"     value="syohin_nm" type="text" size="20" maxlength="20">
                <validate id="maxLength"       value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>

            <message id="" value="譛牙柑譌･" order="13" style="margin:5px 65px 0px 0px;float:left;" />
            <input id="yuko_dt" type="text" size="10" maxlength="8" order="14" value="yuko_dt">
                <validate id="isDate" value="" alert="譌･莉伜ｽ｢蠑上〒縺ｯ縺ゅｊ縺ｾ縺帙ｓ縲�" />
            </input>

            <message order="17"  id="notice1"       value="蝠�蜩√さ繝ｼ繝峨∪縺溘�ｯ蝠�蜩∝錐遘ｰ縺ｮ荳驛ｨ繧貞�･蜉帙＠縺ｦ讀懃ｴ｢繝懊ち繝ｳ繧呈款縺励※縺上□縺輔＞縲�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="18"  id="notice2"       value="蝠�蜩√さ繝ｼ繝峨∝膚蜩∝錐遘ｰ繧貞�･蜉帙＠縺溷�ｴ蜷医�ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="19"  id="notice3"       value="蝠�蜩∵焚縺悟､壹＞蝣ｴ蜷医∵､懃ｴ｢蜿翫�ｳ謾ｹ繝壹�ｼ繧ｸ縺ｫ譎る俣縺後°縺九ｋ縺薙→縺後≠繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <input   order="20"  id="search_button" value="讀懃ｴ｢" type="button" script="doSearch()" style="margin:6px 5px 5px 5px;text-align:center;"></input>
        </panel>
        <list width="450" cols="3">
        	<column order="1" name="Check"         mapping="KEY_NO"  label="" width="10%" style="text-align:center;" />
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (S) -->
        	<!-- <column order="2" name="yuko_dt"       mapping="YUKO_DT" label="譛牙柑髢句ｧ区律" width="20%" style="height:28;text-align:center;" /> -->
            <!-- <column order="3" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="25%" style="height:28;text-align:center;" /> -->
        	<column order="2" name="yuko_dt"       mapping="YUKO_DT" label="譛牙柑髢句ｧ区律" width="20%" style="height:28;text-align:left;" />
            <column order="3" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="25%" style="height:28;text-align:left;" />
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (E) -->
            <column order="4" name="hinmei_kanji_na" mapping="SYOHIN_KANJI_NA" label="蝠�蜩∝錐遘ｰ" width="45%" style="text-align:left;padding-left:5px;" />
        </list>
        <sql>
            <id>SyohinSearchQuery</id>
              <body>SELECT * FROM (SELECT
	              		'2' AS KEY_NO,
	              		YUKO_DT,
	              		SYOHIN_CD,
				        <!-- 2021/11/01 T.Urano Redmine#5113 (S) -->
	              		<!-- SYOHIN_KANJI_NA,  -->
						RTRIM(CONCAT(SYOHIN_KANJI_NA, ' ', KIKAKU_KANJI_NA)) AS SYOHIN_KANJI_NA,
				        <!-- 2021/11/01 T.Urano Redmine#5113 (E) -->
	              		BUNRUI1_CD,
	              		BUNRUI2_CD,
	              		BUNRUI3_CD
	              		FROM R_SYOHIN WHERE SHIIRE_HANBAI_KB='4'
	              	) AS SYOHIN
              </body>
              <where order="1" bind="param0">TRIM(BUNRUI1_CD)=:param0</where>
              <where order="2" bind="param1">TRIM(BUNRUI2_CD)=:param1</where>
              <where order="3" bind="param2">TRIM(BUNRUI3_CD)=:param2</where>
              <where order="4" bind="syohin_cd">SYOHIN_CD LIKE %:syohin_cd%</where>
              <where order="5" bind="syohin_nm">SYOHIN_KANJI_NA LIKE %:syohin_nm%</where>
              <where order="6" bind="yuko_dt">YUKO_DT>=:yuko_dt</where>
            <orderby>YUKO_DT, SYOHIN_CD, SYOHIN_KANJI_NA</orderby>
        </sql>
    </popup>
    <popup id="SyohinSearch1" title="蝠�蜩∵､懃ｴ｢" width="800" height="725" init="0" page="100" description="蝠�蜩∵､懃ｴ｢POPUP逕ｻ髱｢(蛻�鬘�1謖�螳�)">
        <panel width="450" cols="2">
            <input   order="1"  id="param0"        value="param0" type="hidden" source="" />
            <message order="2"  id=""              value="蝠�蜩√さ繝ｼ繝�" style="margin:5px 20px 0px 0px;float:left;" />
            <input   order="3"  id="syohin_cd"     value="syohin_cd" type="text" size="16" maxlength="13" style="float:left;ime-mode:disabled;" script="onblur='syohinOnblur(this);'">
                <validate id="isHalfCharacter" value=""   alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength"       value="13" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ13譁�蟄励〒縺吶�" />
            </input>
            <message order="4"  id=""              value="蝠�蜩∝錐遘ｰ" style="margin:5px 20px 0px 20px;float:left;" />
            <input   order="5"  id="syohin_nm"     value="syohin_nm" type="text" size="22" maxlength="80" style="float:left;">
                <validate id="maxLength"       value="80" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ80譁�蟄励〒縺吶�" />
            </input>
            <input   order="6"  id="search_button" value="讀懃ｴ｢" type="button" script="doSearch()" style="margin:6px 5px 5px 5px;text-align:center;"></input>
            <message order="7"  id="notice1"       value="蝠�蜩√さ繝ｼ繝峨∪縺溘�ｯ蝠�蜩∝錐遘ｰ縺ｮ荳驛ｨ繧貞�･蜉帙＠縺ｦ讀懃ｴ｢繝懊ち繝ｳ繧呈款縺励※縺上□縺輔＞縲�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="8"  id="notice2"       value="蝠�蜩√さ繝ｼ繝峨∝膚蜩∝錐遘ｰ繧貞�･蜉帙＠縺溷�ｴ蜷医�ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="9"  id="notice3"       value="蝠�蜩∵焚縺悟､壹＞蝣ｴ蜷医∵､懃ｴ｢蜿翫�ｳ謾ｹ繝壹�ｼ繧ｸ縺ｫ譎る俣縺後°縺九ｋ縺薙→縺後≠繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
        </panel>
        <list width="450" cols="3">
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (S) -->
            <!-- <column order="1" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="text-align:center;" /> -->
            <column order="1" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="text-align:left;" />
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (E) -->
            <column order="2" name="syohin_kanji_na" mapping="SYOHIN_KANJI_NA" label="蝠�蜩∝錐遘ｰ" width="55%" style="text-align:left;padding-left:5px;" />
            <column order="3" name="Select"          label="" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>SyohinSearchQuery</id>
            <body>SELECT SYOHIN_CD, TRIM(CONCAT(SYOHIN_KANJI_NA, ' ', KIKAKU_KANJI_NA, ' ', KIKAKU_KANJI_2_NA)) SYOHIN_KANJI_NA FROM R_SYOHIN</body>
            <where order="1" bind="param0">TRIM(BUNRUI1_CD)=:param0</where>
            <where order="2" bind="syohin_cd">SYOHIN_CD LIKE %:syohin_cd%</where>
            <where order="3" bind="syohin_nm">SYOHIN_KANJI_NA LIKE %:syohin_nm%</where>
            <orderby>SYOHIN_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="SyohinSearch2" title="蝠�蜩∵､懃ｴ｢" width="800" height="725" init="0" page="100" description="蝠�蜩∵､懃ｴ｢POPUP逕ｻ髱｢(蛻�鬘�2謖�螳�)">
        <panel width="450" cols="2">
            <input   order="1"  id="param0"        value="param0" type="hidden" source="" />
            <message order="2"  id=""              value="蝠�蜩√さ繝ｼ繝�" style="margin:5px 20px 0px 0px;float:left;" />
            <input   order="3"  id="syohin_cd"     value="syohin_cd" type="text" size="16" maxlength="13" style="float:left;ime-mode:disabled;" script="onblur='syohinOnblur(this);'">
                <validate id="isHalfCharacter" value=""   alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength"       value="13" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ13譁�蟄励〒縺吶�" />
            </input>
            <message order="4"  id=""              value="蝠�蜩∝錐遘ｰ" style="margin:5px 22px 0px 20px;float:left;" />
            <input   order="5"  id="syohin_nm"     value="syohin_nm" type="text" size="20" maxlength="80" style="float:left;">
                <validate id="maxLength"       value="80" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ80譁�蟄励〒縺吶�" />
            </input>
            <input   order="6"  id="search_button" value="讀懃ｴ｢" type="button" script="doSearch()" style="margin:6px 5px 5px 5px;text-align:center;"></input>
            <message order="7"  id="notice1"       value="蝠�蜩√さ繝ｼ繝峨∪縺溘�ｯ蝠�蜩∝錐遘ｰ縺ｮ荳驛ｨ繧貞�･蜉帙＠縺ｦ讀懃ｴ｢繝懊ち繝ｳ繧呈款縺励※縺上□縺輔＞縲�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="8"  id="notice2"       value="蝠�蜩√さ繝ｼ繝峨∝膚蜩∝錐遘ｰ繧貞�･蜉帙＠縺溷�ｴ蜷医�ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="9"  id="notice3"       value="蝠�蜩∵焚縺悟､壹＞蝣ｴ蜷医∵､懃ｴ｢蜿翫�ｳ謾ｹ繝壹�ｼ繧ｸ縺ｫ譎る俣縺後°縺九ｋ縺薙→縺後≠繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
        </panel>
        <list width="450" cols="3">
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (S) -->
            <!-- <column order="1" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="text-align:center;" /> -->
            <column order="1" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="text-align:left;" />
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (E) -->
            <column order="2" name="syohin_kanji_na" mapping="SYOHIN_KANJI_NA" label="蝠�蜩∝錐遘ｰ" width="55%" style="text-align:left;padding-left:5px;" />
            <column order="3" name="Select"          label="" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>SyohinSearchQuery</id>
            <body>SELECT SYOHIN_CD, TRIM(CONCAT(SYOHIN_KANJI_NA, ' ', KIKAKU_KANJI_NA, ' ', KIKAKU_KANJI_2_NA)) SYOHIN_KANJI_NA FROM R_SYOHIN</body>
            <where order="1" bind="param0">TRIM(BUNRUI2_CD)=:param0</where>
            <where order="2" bind="syohin_cd">SYOHIN_CD LIKE %:syohin_cd%</where>
            <where order="3" bind="syohin_nm">SYOHIN_KANJI_NA LIKE %:syohin_nm%</where>
            <orderby>SYOHIN_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="SyohinSearch3" title="蝠�蜩∵､懃ｴ｢" width="800" height="725" init="0" page="100" description="蝠�蜩∵､懃ｴ｢POPUP逕ｻ髱｢(蛻�鬘�3謖�螳�)">
        <panel width="450" cols="2">
            <input   order="1"  id="param0"        value="param0" type="hidden" source="" />
            <message order="2"  id=""              value="蝠�蜩√さ繝ｼ繝�" style="margin:5px 20px 0px 0px;float:left;" />
            <input   order="3"  id="syohin_cd"     value="syohin_cd" type="text" size="16" maxlength="13" style="float:left;ime-mode:disabled;" script="onblur='syohinOnblur(this);'">
                <validate id="isHalfCharacter" value=""   alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength"       value="13" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ13譁�蟄励〒縺吶�" />
            </input>
            <message order="4"  id=""              value="蝠�蜩∝錐遘ｰ" style="margin:5px 20px 0px 20px;float:left;" />
            <input   order="5"  id="syohin_nm"     value="syohin_nm" type="text" size="22" maxlength="80" style="float:left;">
                <validate id="maxLength"       value="80" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ80譁�蟄励〒縺吶�" />
            </input>
            <input   order="6"  id="search_button" value="讀懃ｴ｢" type="button" script="doSearch()" style="margin:6px 5px 5px 5px;text-align:center;"></input>
            <message order="7"  id="notice1"       value="蝠�蜩√さ繝ｼ繝峨∪縺溘�ｯ蝠�蜩∝錐遘ｰ縺ｮ荳驛ｨ繧貞�･蜉帙＠縺ｦ讀懃ｴ｢繝懊ち繝ｳ繧呈款縺励※縺上□縺輔＞縲�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="8"  id="notice2"       value="蝠�蜩√さ繝ｼ繝峨∝膚蜩∝錐遘ｰ繧貞�･蜉帙＠縺溷�ｴ蜷医�ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="9"  id="notice3"       value="蝠�蜩∵焚縺悟､壹＞蝣ｴ蜷医∵､懃ｴ｢蜿翫�ｳ謾ｹ繝壹�ｼ繧ｸ縺ｫ譎る俣縺後°縺九ｋ縺薙→縺後≠繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
        </panel>
        <list width="450" cols="3">
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (S) -->
            <!-- <column order="1" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="text-align:center;" /> -->
            <column order="1" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="text-align:left;" />
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (E) -->
            <column order="2" name="syohin_kanji_na" mapping="SYOHIN_KANJI_NA" label="蝠�蜩∝錐遘ｰ" width="55%" style="text-align:left;padding-left:5px;" />
            <column order="3" name="Select"          label="" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>SyohinSearchQuery</id>
            <body>SELECT SYOHIN_CD, TRIM(CONCAT(SYOHIN_KANJI_NA, ' ', KIKAKU_KANJI_NA, ' ', KIKAKU_KANJI_2_NA)) SYOHIN_KANJI_NA FROM R_SYOHIN</body>
            <where order="1" bind="param0">TRIM(BUNRUI3_CD)=:param0</where>
            <where order="2" bind="syohin_cd">SYOHIN_CD LIKE %:syohin_cd%</where>
            <where order="3" bind="syohin_nm">SYOHIN_KANJI_NA LIKE %:syohin_nm%</where>
            <orderby>SYOHIN_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="KYT02007" title="繧ｨ繝ｪ繧｢讀懃ｴ｢" width="800" height="600" init="1" page="0"
        description="繧ｨ繝ｪ繧｢讀懃ｴ｢">
        <panel width="520" cols="1">
            <message id="" value="繧ｨ繝ｪ繧｢繧ｳ繝ｼ繝�" order="1" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="area_cd" type="text" size="10" maxlength="4" order="2" value="area_cd"
                style="ime-mode:disabled;margin:0px 100px 0px 0px;float:left;">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="繧ｨ繝ｪ繧｢蜷咲ｧｰ" order="3" style="margin:5px 28px 0px 0px;float:left;" />
            <input id="area_nm" type="text" size="20" maxlength="20" order="4" value="area_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="5" value="繧ｨ繝ｪ繧｢繧ｳ繝ｼ繝峨√お繝ｪ繧｢蜷咲ｧｰ縺ｨ繧ゅ↓縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="6" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="area_cd" mapping="AREA_CD" label="繧ｨ繝ｪ繧｢繧ｳ繝ｼ繝�" width="30%" order="1"
                style="height:28;text-align:left;" />
            <column name="area_kanji_na" mapping="AREA_KANJI_NA" label="繧ｨ繝ｪ繧｢蜷咲ｧｰ" width="55%" order="2"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" width="15%" order="3" style="text-align:center;" />
        </list>
        <sql>
            <id>AreaSearchQuery</id>
            <body>SELECT AREA_CD, AREA_KANJI_NA FROM R_AREA</body>
            <where order="1" bind="area_cd">AREA_CD LIKE %:area_cd%</where>
            <where order="2" bind="area_nm">AREA_KANJI_NA LIKE %:area_nm%</where>
            <orderby>AREA_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="KYT02008" title="蜿門ｼ募�域､懃ｴ｢" width="800" height="600" init="1" page="50"
        description="蜿門ｼ募�域､懃ｴ｢">
        <panel width="560" cols="2">
            <message id="" value="蜿門ｼ募�医さ繝ｼ繝�" order="1" style="margin:5px 31px 0px 0px;float:left;" />
            <!-- 2021/06/10 T.Kaita Redmine#3012蟇ｾ蠢� (S) -->
       <!-- <input id="torihikisaki_cd" type="text" size="10" maxlength="4" order="2" value="torihikisaki_cd" style="ime-mode:disabled;float:left;"> -->
            <input id="torihikisaki_cd" type="text" size="10" maxlength="5" order="2" value="torihikisaki_cd" style="ime-mode:disabled;float:left;">
            <!-- 2021/06/10 T.Kaita Redmine#3012蟇ｾ蠢� (E) -->
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <!-- 2021/06/10 T.Kaita Redmine#3012蟇ｾ蠢� (S) -->
           <!-- <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" /> -->
                <validate id="maxLength" value="5" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ5譁�蟄励〒縺吶�" />
                <!-- 2021/06/10 T.Kaita Redmine#3012蟇ｾ蠢� (E) -->
            </input>
            <message id="" value="蜿門ｼ募�亥錐遘ｰ" order="3" style="margin:5px 20px 0px 100px;float:left;" />
            <input id="torihikisaki_nm" type="text" size="20" maxlength="20" order="4" value="torihikisaki_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="6" value="蜿門ｼ募�医さ繝ｼ繝峨∝叙蠑募�亥錐遘ｰ縺ｨ繧ゅ↓縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="7" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="torihikisaki_cd" mapping="SHIIRESAKI_CD" label="蜿門ｼ募�医さ繝ｼ繝�" order="1" width="30%"
                style="height:28;text-align:left;" />
            <column name="tohihikisaki_kanji_na" mapping="SHIIRESAKI_KANJI_NA" label="蜿門ｼ募�亥錐遘ｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>TorihikisakiSearchQuery</id>
            <body>SELECT SHIIRESAKI_CD, SHIIRESAKI_KANJI_NA FROM R_SHIIRESAKI</body>
            <where order="1" bind="torihikisaki_cd">SHIIRESAKI_CD LIKE %:torihikisaki_cd%</where>
            <where order="2" bind="torihikisaki_nm">SHIIRESAKI_KANJI_NA LIKE %:torihikisaki_nm%</where>
            <orderby>SHIIRESAKI_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90901" title="蠎苓�玲､懃ｴ｢" width="800" height="600" init="1" page="0"
        description="蠎苓�玲､懃ｴ｢">
        <panel width="520" cols="1">
            <input id="param0" type="text" order="1" value="param0" style="display:none;"></input>
            <message id="" value="蠎苓�励さ繝ｼ繝�" order="1" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="store_cd" type="text" size="12" maxlength="4" order="2" value="store_cd"
                style="ime-mode:disabled;margin:0px 100px 0px 0px;float:left;">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="蠎苓�怜錐遘ｰ" order="3" style="margin:5px 28px 0px 0px;float:left;" />
            <input id="store_nm" type="text" size="20" maxlength="20" order="4" value="store_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="5" value="蠎苓�励さ繝ｼ繝峨∝ｺ苓�怜錐遘ｰ縺ｨ繧ゅ↓縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="6" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="tenpo_cd" mapping="TENPO_CD" label="蠎苓�励さ繝ｼ繝�" width="30%" order="1"
                style="height:28;text-align:left;" />
            <column name="tenpo_kanji_na" mapping="TENPO_KANJI_NA" label="蠎苓�怜錐遘ｰ" width="55%" order="2"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" width="15%" order="3" style="text-align:center;" />
        </list>
        <sql>
            <id>TenpoSearchQuery</id>
            <body>SELECT TENPO_CD, TENPO_KANJI_NA FROM R_TENPO</body>
            <where order="1" bind="store_cd">TENPO_CD LIKE %:store_cd%</where>
            <where order="2" bind="store_nm">TENPO_KANJI_NA LIKE %:store_nm%</where>
            <orderby>TENPO_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90914" title="蠎励げ繝ｫ繝ｼ繝玲､懃ｴ｢" width="800" height="600" init="1" page="0"
        description="蠎励げ繝ｫ繝ｼ繝玲､懃ｴ｢">
            <input id="param0" type="text" order="1" value="param0" style="display:none;"></input>
        <list width="500" cols="3">
            <column name="groupno_cd" mapping="GROUPNO_CD" label="蠎励げ繝ｫ繝ｼ繝励さ繝ｼ繝�" order="1" width="30%"
                style="height:28;text-align:left;" />
            <column name="name_na" mapping="TEN_GROUP_NA" label="蠎励げ繝ｫ繝ｼ繝怜錐遘ｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>TengroupSearchQuery</id>
            <body>SELECT GROUPNO_CD, TEN_GROUP_NA FROM R_TENGROUPNO</body>
            <where order="1" bind="">YOTO_KB=(SELECT TRIM(PARAMETER_TX) FROM SYSTEM_CONTROL WHERE SUBSYSTEM_ID='COMMON' AND PARAMETER_ID='TNP_TENGROUP_YOTO_KB_DEFAULT')</where>
            <where order="2" bind="">BUNRUI_KB=(SELECT TRIM(PARAMETER_TX) FROM SYSTEM_CONTROL WHERE SUBSYSTEM_ID='COMMON' AND PARAMETER_ID='KEY_BUNRUI_TENGROUP')</where>
            <where order="3" bind="">TRIM(BUNRUI_CD) =(SELECT TRIM(PARAMETER_TX) FROM SYSTEM_CONTROL WHERE SUBSYSTEM_ID='COMMON' AND PARAMETER_ID='TNP_TENGROUP_BUNRUI_CD_DEFAULT')</where>
            <orderby>GROUPNO_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="KYT02011" title="繝ｦ繝ｼ繧ｶ讀懃ｴ｢" width="800" height="600" init="1" page="0"
        description="繝ｦ繝ｼ繧ｶ讀懃ｴ｢">
        <panel width="520" cols="1">
            <message id="" value="繝ｦ繝ｼ繧ｶID" order="1" style="margin:5px 30px 0px 0px;float:left;" />
            <input id="user_id" type="text" size="20" maxlength="20" order="2" value="user_id"
                style="ime-mode:disabled;float:left;">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="繝ｦ繝ｼ繧ｶ蜷咲ｧｰ" order="3" style="margin:5px 28px 0px 50px;float:left;" />
            <input id="user_na" type="text" size="20" maxlength="20" order="4" value="user_na">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="5" value="繝ｦ繝ｼ繧ｶID縲√Θ繝ｼ繧ｶ蜷咲ｧｰ縺ｨ繧ゅ↓縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="6" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="600" cols="3">
            <column name="user_id" mapping="USER_ID" label="繝ｦ繝ｼ繧ｶ繧ｳ繝ｼ繝�" width="30%" order="1"
                style="height:28;text-align:center;" />
            <column name="user_na" mapping="USER_NA" label="繝ｦ繝ｼ繧ｶ蜷咲ｧｰ" width="55%" order="2"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" width="15%" order="3" style="text-align:center;" />
        </list>
        <sql>
            <id>UserSearchQuery</id>
            <body>SELECT USER_ID, USER_NA FROM R_PORTAL_USER</body>
            <where order="1" bind="user_id">USER_ID LIKE %:user_id%</where>
            <where order="2" bind="user_na">USER_NA LIKE %:user_na%</where>
            <orderby>USER_ID ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90911" title="繝�繝ｼ繝樊､懃ｴ｢" width="800" height="700" init="1" page="100"
        description="繝�繝ｼ繝槫ｺ苓�玲､懃ｴ｢">
        <panel width="600" cols="2">
            <input id="param0" type="hidden" value="param0" order="1" source=""></input>
            <message id="" value="繝�繝ｼ繝槫錐" order="4" style="margin:7px 35px 0px 0px;float:left;" />
            <input id="theme_nm" type="text" size="20" maxlength="80" order="5" value="theme_nm" style="margin-top: 3px;margin-right: 100px;">
                <validate id="maxLength" value="80" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ80譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="雋ｩ螢ｲ譛滄俣" order="6" style="margin:7px 31px 0px 0px;float:left;color:#FFFFFF;" />
            <input id="hanbai_start" type="text" size="10" maxlength="8" order="7" value="hanbai_start"
                style="ime-mode:disabled;float:left; margin-top: 3px; ">
                <validate id="isDate" value="" alert="譌･莉伜ｽ｢蠑上〒縺ｯ縺ゅｊ縺ｾ縺帙ｓ縲�" />
                <validate id="compareDate" value="hanbai_end" alert="譌･莉倥′豁｣縺励￥縺ｪ縺�縺ｧ縺吶�" />
            </input>
            <message id="" value="�ｽ�" order="8" style="margin:7px 10px 0px 10px;float:left;" />
            <input id="hanbai_end" type="text" size="10" maxlength="8" order="9" value="hanbai_end"
                style="ime-mode:disabled;float:left; margin-top: 3px; ">
                <validate id="isDate" value="" alert="譌･莉伜ｽ｢蠑上〒縺ｯ縺ゅｊ縺ｾ縺帙ｓ縲�" />
            </input>
            <message id="" value="(yyyymmdd)" order="10" style="margin:7px 0px 2px 10px;" />
            <message id="" value="迚ｹ螢ｲ蛹ｺ蛻�" order="11" style="margin:12px 35px 0px 0px;float:left;white-space: nowrap;" />
            <radio id="tokubai_syubetu" order="12" style="margin:11px 0px 0px 0px; white-space: normal">
                <button label="蜈ｨ縺ｦ"         order="1" value="" checked="true" />
                <button label="繝√Λ繧ｷ"         order="2" value="1" />
                <button label="譛滄俣迚ｹ螢ｲ"           order="3" value="2" />
            </radio>
            <input id="search_button" type="button" order="13" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="600" cols="4">
            <column name="theme_cd" mapping="THEME_CD" label="繝�繝ｼ繝槭さ繝ｼ繝�" width="15%" order="1"
                style="height:28;text-align:center;" />
            <column name="theme_nm" mapping="THEME_NA" label="繝�繝ｼ繝槫錐" width="40%" order="2"
                style="text-align:left;padding-left:5px;" />
            <column name="hanbai" mapping="HANBAI_START_DT_HANBAI_END_DT"
                label="雋ｩ螢ｲ譛滄俣" width="35%" order="3" style="text-align:center;" />
            <column name="Select" label="" width="10%" order="4" style="text-align:center;" />
        </list>
        <sql>
            <id>ThemeSearchQuery</id>
            <body>
                SELECT
                    RT.THEME_CD,
                    RTH.THEME_NA,
                   CASE
                        WHEN  RTH.THEME_HANBAI_START_DT IS NULL THEN '縲'
				        ELSE  CONCAT(SUBSTRING(RTH.THEME_HANBAI_START_DT, 1, 4), '/',  SUBSTRING(RTH.THEME_HANBAI_START_DT, 5, 2), '/', SUBSTRING(RTH.THEME_HANBAI_START_DT, 7, 2))
					END
					+
					CASE
                        WHEN  (RTH.THEME_HANBAI_START_DT IS NULL) AND (RTH.THEME_HANBAI_END_DT IS NULL) THEN '縲'
				        ELSE  '�ｽ�'
					END
					 +
					CASE
                        WHEN  RTH.THEME_HANBAI_END_DT IS NULL THEN '縲'
				        ELSE  CONCAT(SUBSTRING(RTH.THEME_HANBAI_START_DT, 1, 4), '/',  SUBSTRING(RTH.THEME_HANBAI_START_DT, 5, 2), '/', SUBSTRING(RTH.THEME_HANBAI_START_DT, 7, 2))
					END	  AS HANBAI_START_DT_HANBAI_END_DT,
                    CASE
                        WHEN  DTT.MIN_HACHU_DT IS NULL THEN '縲'
                        ELSE CONCAT(SUBSTRING(DTT.MIN_HACHU_DT, 1, 4), '/',  SUBSTRING(DTT.MIN_HACHU_DT, 5, 2), '/', SUBSTRING(DTT.MIN_HACHU_DT, 7, 2))
                    END AS MIN_HACHU_DT
                FROM
                    (SELECT
                        TENPO_CD, THEME_CD
                    FROM
                        R_TOKUBAI
                    GROUP BY
                        TENPO_CD, THEME_CD
                    ) RT
                LEFT OUTER JOIN DT_TENPO_THEME DTT
                    ON  DTT.TENPO_CD  = RT.TENPO_CD
                    AND DTT.THEME_CD = RT.THEME_CD
                LEFT OUTER JOIN R_THEME RTH
                    ON RTH.THEME_CD = RT.THEME_CD
            </body>
            <where order="1" bind="param0">TRIM(RT.TENPO_CD) = :param0</where>
            <where order="3" bind="theme_nm">RTH.THEME_NA LIKE %:theme_nm%</where>
            <where order="4" bind="hanbai_start|hanbai_end">
                ((RTH.THEME_HANBAI_START_DT BETWEEN :hanbai_start AND :hanbai_end) OR (RTH.THEME_HANBAI_END_DT
                BETWEEN :hanbai_start AND :hanbai_end))
            </where>
            <where order="5" bind="!hanbai_start|hanbai_end">RTH.THEME_HANBAI_END_DT&lt;=:hanbai_end</where>
            <where order="6" bind="hanbai_start|!hanbai_end">RTH.THEME_HANBAI_START_DT&gt;=:hanbai_start</where>
            <where order="7" bind="tokubai_syubetu">
            	EXISTS (
            		SELECT
            			1
            		 FROM
            		 	 R_TOKUBAI RT2
            		 WHERE
            		     RT2.TENPO_CD = RT.TENPO_CD
					 AND RT2.THEME_CD = RT.THEME_CD
					 AND RT2.TOKUBAI_KB = :tokubai_syubetu
				)
            </where>
            <orderby>
                CASE WHEN ISNULL(DTT.MIN_HACHU_DT, '') = '' THEN 1 ELSE 0 END ASC,
                DTT.MIN_HACHU_DT ASC,
                CASE WHEN ISNULL(RTH.THEME_HANBAI_START_DT, '') = '' THEN 1 ELSE 0 END ASC,
                RTH.THEME_HANBAI_START_DT ASC,
                CASE WHEN ISNULL(RTH.THEME_HANBAI_END_DT, '') = '' THEN 1 ELSE 0 END ASC,
                RTH.THEME_HANBAI_END_DT ASC,
                CASE WHEN ISNULL(RTH.THEME_SAKUSEISYA_KB, '') = '' THEN 1 ELSE 0 END ASC,
                RTH.THEME_SAKUSEISYA_KB ASC
            </orderby>
        </sql>
    </popup>
     <popup id="AUT90913" title="譽夂分讀懃ｴ｢" width="800" height="600" init="1" page="100"
        description="譽夂分讀懃ｴ｢">
        <panel width="520" cols="1">
        	<!-- 2021/06/18 T.Kaita Redmine#1855蟇ｾ蠢�(譽壼牡繝槭せ繧ｿ蛻�鬘槫ｻ�豁｢) (S)  -->
        	<!--
            <message id="" value="* 螟ｧ蛻�鬘槭さ繝ｼ繝�" order="1" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="param1" type="text" size="10" maxlength="4" order="2" value="param1"
                style="float:left;background-color:#e0e0e0;color:#383838;float:left;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            -->
            <!-- 2021/06/18 T.Kaita Redmine#1855蟇ｾ蠢�(譽壼牡繝槭せ繧ｿ蛻�鬘槫ｻ�豁｢) (E)  -->
            <message id="" value="* 蠎苓�励さ繝ｼ繝�" order="3" style="margin:5px 28px 0px 5px;float:left;" />
            <input id="param0" type="text" size="12" maxlength="4" order="4" value="param0"
            style="background-color:#e0e0e0;color:#383838;" readonly="true" source="">
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <input id="param2" type="text" order="1" value="param2" style="display:none;"></input>
        </panel>
        <list width="500" cols="3">
            <column name="tana_nb" mapping="TANA_NB" label="譽夂分" width="30%" order="1"
                style="height:28;text-align:left;" />
            <column name="tana_na" mapping="TANA_NA" label="譽夂分蜷咲ｧｰ" width="55%" order="2"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" width="15%" order="3" style="text-align:center;" />
        </list>
        <sql>
            <id>TananbSearchQuery</id>
            <body>
                SELECT
                    RG.GONDOLA_NB AS TANA_NB,
                    RG.GONDOLA_NA AS TANA_NA
                FROM
                    R_GONDOLA RG
            </body>
            <where order="1" bind="param0">RG.TENPO_CD=LEFT(CONCAT(:param0 , '          ') , 10)</where>
            <!-- 2021/06/18 T.Kaita Redmine#1855蟇ｾ蠢�(譽壼牡繝槭せ繧ｿ蛻�鬘槫ｻ�豁｢) (S)  -->
            <!--
            <where order="2" bind="param1">RG.BUNRUI_CD=LEFT(CONCAT(:param1 , '          ') , 10)</where>
            <where order="3" bind="">RG.BUNRUI_KB=(SELECT LTRIM(RTRIM(PARAMETER_TX)) FROM SYSTEM_CONTROL WHERE SUBSYSTEM_ID='COMMON' AND PARAMETER_ID='TNP_TANAWARI_BUNRUI_KB')</where>
 			-->
            <!-- 2021/06/18 T.Kaita Redmine#1855蟇ｾ蠢�(譽壼牡繝槭せ繧ｿ蛻�鬘槫ｻ�豁｢) (E)  -->
            <orderby>RG.BUNRUI_CD, RG.GONDOLA_NB</orderby>
        </sql>
    </popup>
    <popup id="KYT02014" title="繧ｴ繝ｳ繝峨Λ讀懃ｴ｢�ｼ磯ｱ髢鍋匱豕ｨ逕ｨ�ｼ�" width="800" height="600" init="1" page="100"
        description="繧ｴ繝ｳ繝峨Λ讀懃ｴ｢">
        <panel width="520" cols="1">
            <message id="" value="* 荳ｭ蛻�鬘槭さ繝ｼ繝�" order="1" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="param1" type="text" size="10" maxlength="6" order="2" value="param1"
                style="float:left;background-color:#e0e0e0;color:#383838;float:left;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="6" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ6譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="* 蠎苓�励さ繝ｼ繝�" order="3" style="margin:5px 28px 0px 100px;float:left;" />
            <input id="param0" type="text" size="12" maxlength="4" order="4" value="param0"
            style="background-color:#e0e0e0;color:#383838;" readonly="true" source="">
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="tana_nb" mapping="TANA_NB" label="繧ｴ繝ｳ繝峨Λ" width="30%" order="1"
                style="height:28;text-align:left;" />
            <column name="first_syohin_na" mapping="FIRST_SYOHIN_NA" label="蜈磯�ｭ蝠�蜩∝錐" width="55%" order="2"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" width="15%" order="3" style="text-align:center;" />
        </list>
        <sql>
            <id>TananbSearchQuery</id>
            <body>
                SELECT
                    RG.GONDOLA_NB AS TANA_NB,
                    RG.TOP_SYOHIN_NA AS FIRST_SYOHIN_NA
                FROM
                    R_GONDOLA RG
            </body>
            <where order="1" bind="param0">RG.TENPO_CD=LEFT( CONCAT(:param0, '          ') , 10)</where>
            <where order="2" bind="param1">RG.BUNRUI_CD=LEFT( CONCAT(:param1, '          ') , 10)</where>
            <where order="3" bind="">RG.BUNRUI_KB=(SELECT TRIM(PARAMETER_TX) FROM SYSTEM_CONTROL WHERE SUBSYSTEM_ID='COMMON' AND PARAMETER_ID='TNP_TANAWARI_BUNRUI_KB')</where>
            <orderby>RG.BUNRUI_CD, RG.GONDOLA_NB</orderby>
        </sql>
    </popup>
    <popup id="AUT90915" title="蝠�蜩√げ繝ｫ繝ｼ繝玲､懃ｴ｢" width="800" height="600" init="1" page="0"
        description="蝠�蜩√げ繝ｫ繝ｼ繝玲､懃ｴ｢">
        <panel width="520" cols="2">
            <message id="" value="* 荳ｭ蛻�鬘槭さ繝ｼ繝�" order="1" style="margin:5px 36px 0px 0px;float:left;" />
            <input id="param0" type="text" size="10" maxlength="6" order="2" value="param0"
                style="ime-mode:disabled;background-color:#e0e0e0;color:#383838;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="6" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ6譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="蝠�蜩√げ繝ｫ繝ｼ繝怜錐遘ｰ" order="3" style="margin:5px 15px 0px 0px;float:left;" />
            <input id="group_nm" type="text" size="20" maxlength="20" order="4" value="group_nm" >
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="5" value="蝠�蜩√げ繝ｫ繝ｼ繝怜錐遘ｰ縺ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="6" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="syohin_group_cd" mapping="SYOHIN_GROUP_CD" label="蝠�蜩√げ繝ｫ繝ｼ繝励さ繝ｼ繝�" order="1" width="30%"
                style="height:28;text-align:left;" />
            <column name="syohin_group_na" mapping="SYOHIN_GROUP_NA" label="蝠�蜩√げ繝ｫ繝ｼ繝怜錐遘ｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>SyohinGroupSearchQuery</id>
            <body>SELECT SYOHIN_GROUP_CD, SYOHIN_GROUP_NA FROM R_SYOHIN_GROUP</body>
            <where order="1" bind="">BUNRUI_KB=(SELECT TRIM(PARAMETER_TX) FROM SYSTEM_CONTROL WHERE SUBSYSTEM_ID='COMMON' AND PARAMETER_ID='KEY_BUNRUI_SYOHIN_GROUP')</where>
            <where order="2" bind="param0">TRIM(BUNRUI_CD)=:param0</where>
            <where order="3" bind="group_nm">SYOHIN_GROUP_NA LIKE %:group_nm%</where>
            <orderby>SYOHIN_GROUP_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT02053" title="騾∬ｾｼ逡ｪ蜿ｷ讀懃ｴ｢" width="900" height="630" init="1" page="100"
        description="騾∬ｾｼ逡ｪ蜿ｷ讀懃ｴ｢">
        <panel width="600" cols="2">
            <message id="" value="騾∬ｾｼ蜷咲ｧｰ" order="2" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="okurikomi_na" type="text" size="44" maxlength="20" order="3" value="okurikomi_na">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="4" value="騾∬ｾｼ蜷咲ｧｰ縺ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="5" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="okurikomi_no" mapping="OKURIKOMI_NO" label="騾∬ｾｼ逡ｪ蜿ｷ" order="1" width="30%"
                style="height:28px;text-align:left;" />
            <column name="okurikomi_na" mapping="OKURIKOMI_NA" label="騾∬ｾｼ蜷咲ｧｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="select_button" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>OkurikomiSearchQuery</id>
            <body>
                SELECT
                    DOHH.OKURIKOMI_NO
                    ,DOHH.OKURIKOMI_NA
                FROM
                    DT_OKURIKOMI_HACHU_HEADER DOHH
            </body>
            <where order="1" bind="">DOHH.DELETE_FG != '1'</where>
            <where order="2" bind="okurikomi_na">DOHH.OKURIKOMI_NA LIKE %:okurikomi_na%</where>
            <orderby>DOHH.OKURIKOMI_NO ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90916" title="蝠�蜩∵､懃ｴ｢" width="800" height="725" init="0" page="100" description="蝠�蜩∵､懃ｴ｢(蛻�鬘�1謖�螳�)">
        <panel width="520" cols="2">
            <message id="" value="*驛ｨ髢繧ｳ繝ｼ繝�" order="1" style="margin:5px 5px 0px 0px;float:left;" />
            <input id="param0" type="text" size="10" maxlength="2" order="2" value="param0"
                style="ime-mode:disabled;float:left;" source="" script="onblur='bunruiOnblur(this,1)'">
                <validate id="checkRequired" value="" alert="驛ｨ髢繧ｳ繝ｼ繝牙ｿ�鬆亥�･蜉幃��逶ｮ縺ｧ縺吶�"/>
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="2" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ2譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="螟ｧ蛻�鬘槭さ繝ｼ繝�" order="3" style="margin:5px 5px 0px 100px;float:left;" />
            <input id="param1" type="text" size="10" maxlength="4" order="4" value="param1" style="ime-mode:disabled;" script="onblur='bunruiOnblur(this,2)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="荳ｭ蛻�鬘槭さ繝ｼ繝�" order="5" style="margin:5px 13px 0px 0px;float:left;" />
            <input id="param2" type="text" size="10" maxlength="6" order="6" value="param2" style="ime-mode:disabled;float:left;" script="onblur='bunruiOnblur(this,3)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="6" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ6譁�蟄励〒縺吶�" />
            </input>
            <message order="7"  id=""              value="蝠�蜩√さ繝ｼ繝�" style="margin:5px 16px 0px 100px;float:left;" />
            <input   order="8"  id="syohin_cd"     value="syohin_cd" type="text" size="16" maxlength="13" style="ime-mode:disabled;" script="onblur='syohinOnblur(this);'">
                <validate id="isHalfCharacter" value=""   alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength"       value="13" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ13譁�蟄励〒縺吶�" />
            </input>
            <message order="9"  id=""              value="蝠�蜩∝錐遘ｰ" style="margin:5px 28px 0px 0px;float:left;" />
            <input   order="10"  id="syohin_nm"     value="syohin_nm" type="text" size="20" maxlength="20">
                <validate id="maxLength"       value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message order="11"  id="notice1"       value="蝠�蜩√さ繝ｼ繝峨∪縺溘�ｯ蝠�蜩∝錐遘ｰ縺ｮ荳驛ｨ繧貞�･蜉帙＠縺ｦ讀懃ｴ｢繝懊ち繝ｳ繧呈款縺励※縺上□縺輔＞縲�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="12"  id="notice2"       value="蝠�蜩√さ繝ｼ繝峨∝膚蜩∝錐遘ｰ繧貞�･蜉帙＠縺溷�ｴ蜷医�ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="13"  id="notice3"       value="蝠�蜩∵焚縺悟､壹＞蝣ｴ蜷医∵､懃ｴ｢蜿翫�ｳ謾ｹ繝壹�ｼ繧ｸ縺ｫ譎る俣縺後°縺九ｋ縺薙→縺後≠繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <input   order="14"  id="search_button" value="讀懃ｴ｢" type="button" script="doSearch()" style="margin:6px 5px 5px 5px;text-align:center;"></input>
        </panel>
        <list width="450" cols="3">
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (S) -->
            <!-- <column order="1" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="height:28;text-align:center;" /> -->
            <column order="1" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="height:28;text-align:left;" />
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (E) -->
            <column order="2" name="hinmei_kanji_na" mapping="HINMEI_KANJI_NA" label="蝠�蜩∝錐遘ｰ" width="55%" style="text-align:left;padding-left:5px;" />
            <column order="3" name="Select"          label="" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>SyohinSearchQuery</id>
            <body>SELECT SYOHIN_CD, TRIM(CONCAT(KIKAKU_KANJI_2_NA, ' ', SYOHIN_KANJI_NA, ' ', KIKAKU_KANJI_NA)) HINMEI_KANJI_NA FROM R_SYOHIN</body>
            <where order="1" bind="param0">TRIM(BUNRUI1_CD)=:param0</where>
            <where order="2" bind="param1">TRIM(BUNRUI2_CD)=:param1</where>
            <where order="3" bind="param2">TRIM(BUNRUI3_CD)=:param2</where>
            <where order="4" bind="syohin_cd">SYOHIN_CD LIKE %:syohin_cd%</where>
            <where order="5" bind="syohin_nm">SYOHIN_KANJI_NA LIKE %:syohin_nm%</where>
            <orderby>SYOHIN_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="KYT02103" title="逋ｺ豕ｨNo讀懃ｴ｢" width="600" height="660" init="1" page="0" description="逋ｺ豕ｨNo讀懃ｴ｢">

        <panel width="520" cols="2">
            <input id="param0" type="hidden" value="param0" order="1" source=""></input>
            <message id="" value="逋ｺ豕ｨNo" order="2" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="hachusyo_nb" type="text" size="20" maxlength="20" order="3" value="hachusyo_nb" style="float:left;">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="逋ｺ豕ｨ蜷咲ｧｰ" order="4" style="margin:5px 20px 0px 100px;float:left;" />
            <input id="hachusyo_na" type="text" size="20" maxlength="20" order="5" value="hachusyo_na">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="6" value="逋ｺ豕ｨNo縲∫匱豕ｨ蜷咲ｧｰ縺ｨ繧ゅ↓縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="7" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="hachusyo_nb" mapping="HACHU_NO" label="逋ｺ豕ｨNo" order="1" width="30%"
                style="height:28;text-align:left;" />
            <column name="hachusyo_na" mapping="HACHU_NA" label="逋ｺ豕ｨ蜷咲ｧｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
		<sql>
			<id>HachuSyoNbSearchQuery</id>
			<body>SELECT HACHU_NO,HACHU_NA FROM DT_HONBU_HACHU_HEADER </body>
			<where order="1" bind="hachusyo_nb">HACHU_NO LIKE %:hachusyo_nb%</where>
			<where order="2" bind="hachusyo_na">HACHU_NA LIKE %:hachusyo_na%</where>
			<orderby>HACHU_NO ASC</orderby>
		</sql>
	</popup>
	<popup id="KYT02014" title="繝ｦ繝ｼ繧ｶID讀懃ｴ｢" width="800" height="620"
		init="1" page="100" description="繝ｦ繝ｼ繧ｶID讀懃ｴ｢">
		<panel width="520" cols="2">
			<input id="param0" type="hidden" value="param0" order="1"
				source=""></input>
			<message id="" value="繝ｦ繝ｼ繧ｶID" order="2"
				style="margin:5px 20px 0px 0px;float:left;" />
			<input id="by_no" type="text" size="12" maxlength="20"
				order="3" value="by_no"
				style="margin:0px 20px 0px 0px;float:left;">
				<validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
				<validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
			</input>
			<message id="" value="繝ｦ繝ｼ繧ｶ蜷咲ｧｰ" order="4"
				style="margin:5px 20px 0px 20px;float:left;" />
			<input id="by_na" type="text" size="20" maxlength="20"
				order="5" value="by_na">
				<validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
			</input>
			<message id="notice" order="6" value="繝ｦ繝ｼ繧ｶID縲√Θ繝ｼ繧ｶ蜷咲ｧｰ縺ｨ繧ゅ↓縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
				style="margin:5px 5px 5px 0px;text-align:left;" />
			<input id="search_button" type="button" order="7" value="讀懃ｴ｢"
				script="doSearch()" style="margin:6px 5px 5px 5px;text-align:center;">
			</input>
		</panel>
		<list width="500" cols="3">
			<column name="by_no" mapping="BY_NO" label="繝ｦ繝ｼ繧ｶID"
				width="166" order="1" style="height:28;text-align:left; overflow: hidden;" />
			<column name="by_na" mapping="BY_NA" label="繝ｦ繝ｼ繧ｶ蜷咲ｧｰ"
				width="270" order="2" style="text-align:left;padding-left:5px; " />
			<column name="Select" label="" width="61" order="3"
				style="text-align:center; " />
		</list>
		<sql>
			<id>BuyerSearchQuery</id>
			<body>
				SELECT RPU1.USER_ID AS BY_NO,
						RPU1.USER_NA AS BY_NA
				FROM
					R_PORTAL_USER RPU1
			</body>
			<where order="1" bind="by_no">RPU1.USER_ID LIKE %:by_no%</where>
			<where order="2" bind="by_na">RPU1.USER_NA LIKE %:by_na%</where>
			<where order="3" bind="param0">:param0 BETWEEN RPU1.YUKO_DT AND RPU1.YUKO_END_DT
		 		GROUP BY  RPU1.USER_ID, RPU1.USER_NA, RPU1.YUKO_DT
			</where>
			<orderby>RPU1.USER_ID ASC</orderby>
		</sql>
	</popup>
    <popup id="AUT90917" title="蠎苓�玲､懃ｴ｢" width="800" height="600" init="1" page="0"
        description="蠎苓�玲､懃ｴ｢">
        <panel width="520" cols="1">
            <input id="param0" type="text" order="1" value="param0" style="display:none;"></input>
            <message id="" value="蠎苓�励さ繝ｼ繝�" order="1" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="store_cd" type="text" size="12" maxlength="4" order="2" value="store_cd"
                style="ime-mode:disabled;margin:0px 100px 0px 0px;float:left;">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="蠎苓�怜錐遘ｰ" order="3" style="margin:5px 28px 0px 0px;float:left;" />
            <input id="store_nm" type="text" size="20" maxlength="20" order="4" value="store_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="5" value="蠎苓�励さ繝ｼ繝峨∝ｺ苓�怜錐遘ｰ縺ｨ繧ゅ↓縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="6" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="tenpo_cd" mapping="TENPO_CD" label="蠎苓�励さ繝ｼ繝�" width="30%" order="1"
                style="height:28;text-align:left;" />
            <column name="tenpo_kanji_na" mapping="TENPO_KANJI_NA" label="蠎苓�怜錐遘ｰ" width="55%" order="2"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" width="15%" order="3" style="text-align:center;" />
        </list>
        <sql>
            <id>TenpoSearchQuery</id>
            <body>SELECT TENPO_CD AS TENPO_CD, KANJI_NA AS TENPO_KANJI_NA FROM MST_R_TENPO</body>
            <where order="1" bind="param0">:param0 BETWEEN YUKO_DT AND YUKO_END_DT</where>
            <where order="2" bind="store_cd">TENPO_CD LIKE %:store_cd%</where>
            <where order="3" bind="store_nm">KANJI_NA LIKE %:store_nm%</where>
            <where order="4" bind="">DELETE_FG = '0'</where>
            <orderby>TENPO_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90918" title="驛ｨ髢讀懃ｴ｢" width="800" height="600" init="1" page="0"
        description="驛ｨ髢讀懃ｴ｢">
        <panel width="300" cols="2">
            <input id="param0" type="hidden" value="param0" order="1" source=""></input>
            <input id="param1" type="hidden" value="param1" order="2" source=""></input>
            <message id="" value="驛ｨ髢蜷咲ｧｰ" order="3" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="bunrui1_nm" type="text" size="20" maxlength="20" order="4" value="bunrui1_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="5" value="驛ｨ髢蜷咲ｧｰ縺ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="6" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="bunrui1_cd" mapping="BUNRUI1_CD" label="驛ｨ髢繧ｳ繝ｼ繝�" order="1" width="30%"
                style="height:28;text-align:left;" />
            <column name="bunrui1_kanji_na" mapping="BUNRUI1_KANJI_NA" label="驛ｨ髢蜷咲ｧｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>Bunrui1SearchQuery</id>
            <body>
                SELECT DISTINCT
                      RST.BUNRUI1_CD
                    , RB1.BUNRUI1_KANJI_NA
                FROM
                    MST_VW_SYOHIN_TAIKEI RST
                INNER JOIN (
                    SELECT
                          R.BUNRUI1_CD
                        , R.BUNRUI1_KANJI_NA
                        , R.YUKO_DT
                        , R.YUKO_END_DT
                    FROM
                        MST_R_BUNRUI1 R
                    WHERE
                        R.DELETE_FG = '0'
                ) RB1
                ON
                    RST.BUNRUI1_CD = RB1.BUNRUI1_CD
            </body>
            <where order="1" bind="param0">RST.SYSTEM_KB=:param0</where>
            <where order="2" bind="param1">:param1 BETWEEN RB1.YUKO_DT AND RB1.YUKO_END_DT</where>
            <where order="3" bind="bunrui1_nm">RB1.BUNRUI1_KANJI_NA LIKE %:bunrui1_nm%</where>
            <orderby>RST.BUNRUI1_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90919" title="螟ｧ蛻�鬘樊､懃ｴ｢" width="800" height="600" init="1" page="0"
        description="螟ｧ蛻�鬘樊､懃ｴ｢">
        <panel width="520" cols="2">
            <input id="param0" type="hidden" value="param0" order="1" source=""></input>
            <input id="param1" type="hidden" value="param1" order="2" source=""></input>
            <message id="" value="* 驛ｨ髢繧ｳ繝ｼ繝�" order="3" style="margin:5px 10px 0px 0px;float:left;" />
            <input id="param2" type="text" size="10" maxlength="2" order="4" value="param2"
                style="float:left;background-color:#e0e0e0;color:#383838;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="2" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ2譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="螟ｧ蛻�鬘槫錐遘ｰ" order="5" style="margin:5px 8px 0px 100px;float:left;" />
            <input id="bunrui2_nm" type="text" size="20" maxlength="20" order="6" value="bunrui2_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="7" value="螟ｧ蛻�鬘槫錐遘ｰ縺ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="8" value="讀懃ｴ｢" script="doSearch()"
                style="margin:2px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="bunrui2_cd" mapping="BUNRUI2_CD" label="螟ｧ蛻�鬘槭さ繝ｼ繝�" order="1" width="30%"
                style="height:28;text-align:left;" />
            <column name="bunrui2_kanji_na" mapping="BUNRUI2_KANJI_NA" label="螟ｧ蛻�鬘槫錐遘ｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>Bunrui2SearchQuery</id>
            <body>
                SELECT DISTINCT
                      RST.BUNRUI2_CD
                    , RB2.BUNRUI2_KANJI_NA
                FROM
                    MST_VW_SYOHIN_TAIKEI RST
                INNER JOIN (
                    SELECT
                          R.BUNRUI2_CD
                        , R.BUNRUI2_KANJI_NA
                        , R.YUKO_DT
                        , R.YUKO_END_DT
                    FROM
                        MST_R_BUNRUI2 R
                    WHERE
                        R.DELETE_FG = '0'
                ) RB2
                ON
                    RST.BUNRUI2_CD = RB2.BUNRUI2_CD
            </body>
            <where order="1" bind="param0">RST.SYSTEM_KB=:param0</where>
            <where order="2" bind="param1">:param1 BETWEEN RB2.YUKO_DT AND RB2.YUKO_END_DT</where>
            <where order="3" bind="param2">RST.BUNRUI1_CD=:param2</where>
            <where order="4" bind="bunrui2_nm">RB2.BUNRUI2_KANJI_NA LIKE %:bunrui2_nm%</where>
            <orderby>RST.BUNRUI2_CD ASC</orderby>
        </sql>
    </popup>
    <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (S) -->
    <popup id="AUT90927" title="荳ｭ蛻�鬘樊､懃ｴ｢" width="800" height="600" init="1" page="0"
        description="荳ｭ蛻�鬘樊､懃ｴ｢">
        <panel width="520" cols="2">
            <input id="param0" type="hidden" value="param0" order="1" source=""></input>
            <input id="param1" type="hidden" value="param1" order="2" source=""></input>
            <message id="" value="* 驛ｨ髢繧ｳ繝ｼ繝�" order="3" style="margin:5px 31px 0px 0px;float:left;" />
            <input id="param2" type="text" size="10" maxlength="2" order="4" value="param2"
                style="float:left; background-color:#e0e0e0; color:#383838;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="2" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ2譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="* 螟ｧ蛻�鬘槭さ繝ｼ繝�" order="5" style="margin:5px 8px 0px 100px;float:left;" />
            <input id="param3" type="text" size="10" maxlength="4" order="6" value="param3"
                style="background-color:#e0e0e0; color:#383838;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="荳ｭ蛻�鬘槫錐遘ｰ" order="7" style="margin:5px 50px 0px 0px;float:left;" />
            <input id="bunrui3_nm" type="text" size="20" maxlength="20" order="8" value="bunrui3_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="9" value="荳ｭ蛻�鬘槫錐遘ｰ縺ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="10" value="讀懃ｴ｢" script="doSearch()"
                style="margin:2px 5px 6px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (S) -->
            <!-- <column name="bunrui3_cd" mapping="BUNRUI3_CD" label="荳ｭ蛻�鬘槭さ繝ｼ繝�" order="1" width="30%" -->
            <!-- style="text-align:center;" /> -->
            <column name="bunrui3_cd" mapping="BUNRUI3_CD" label="荳ｭ蛻�鬘槭さ繝ｼ繝�" order="1" width="30%"
                style="text-align:left;" />
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (E) -->
            <column name="bunrui3_kanji_na" mapping="BUNRUI3_KANJI_NA" label="荳ｭ蛻�鬘槫錐遘ｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>Bunrui3SearchQuery</id>
            <body>
                SELECT DISTINCT
                      RST.BUNRUI3_CD
                    , RB3.BUNRUI3_KANJI_NA
                FROM
                    MST_VW_SYOHIN_TAIKEI RST
                INNER JOIN (
                    SELECT
                          R.BUNRUI3_CD
                        , R.BUNRUI3_KANJI_NA
                        , R.YUKO_DT
                        , R.YUKO_END_DT
                    FROM
                        MST_R_BUNRUI3 R
                    WHERE
                        R.DELETE_FG = '0'
                ) RB3
                ON
                    RST.BUNRUI3_CD = RB3.BUNRUI3_CD
            </body>
            <where order="1" bind="param0">RST.SYSTEM_KB=:param0</where>
            <where order="2" bind="param1">:param1 BETWEEN RB3.YUKO_DT AND RB3.YUKO_END_DT</where>
            <where order="3" bind="param2">RST.BUNRUI1_CD=:param2</where>
            <where order="4" bind="param3">RST.BUNRUI2_CD=:param3</where>
            <where order="5" bind="bunrui5_nm">RB3.BUNRUI3_KANJI_NA LIKE %:bunrui3_nm%</where>
            <orderby>RST.BUNRUI3_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90920" title="蟆丞��鬘樊､懃ｴ｢" width="800" height="600" init="1" page="0"
        description="蟆丞��鬘樊､懃ｴ｢">
        <panel width="520" cols="2">
            <input id="param0" type="hidden" value="param0" order="1" source=""></input>
            <input id="param1" type="hidden" value="param1" order="2" source=""></input>
            <message id="" value="* 驛ｨ髢繧ｳ繝ｼ繝�" order="3" style="margin:5px 35px 0px 0px;float:left;" />
            <input id="param2" type="text" size="10" maxlength="2" order="4" value="param2"
                style="float:left; background-color:#e0e0e0; color:#383838;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="2" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ2譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="* 螟ｧ蛻�鬘槭さ繝ｼ繝�" order="5" style="margin:5px 8px 0px 75px;float:left;" />
            <input id="param3" type="text" size="10" maxlength="4" order="6" value="param3"
                style="background-color:#e0e0e0; color:#383838;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="* 荳ｭ蛻�鬘槭さ繝ｼ繝�" order="7" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="param4" type="text" size="10" maxlength="6" order="8" value="param4"
                style="float:left;background-color:#e0e0e0; color:#383838;" readonly="true" source="">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="6" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ6譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="蟆丞��鬘槫錐遘ｰ" order="9" style="margin:5px 25px 0px 75px;float:left;" />
            <input id="bunrui5_nm" type="text" size="20" maxlength="20" order="10" value="bunrui5_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="11" value="蟆丞��鬘槫錐遘ｰ縺ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="12" value="讀懃ｴ｢" script="doSearch()"
                style="margin:2px 5px 6px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (S) -->
            <!-- <column name="bunrui5_cd" mapping="BUNRUI5_CD" label="蟆丞��鬘槭さ繝ｼ繝�" order="1" width="30%" -->
                <!-- style="text-align:center;" /> -->
            <column name="bunrui5_cd" mapping="BUNRUI5_CD" label="蟆丞��鬘槭さ繝ｼ繝�" order="1" width="30%"
                style="text-align:left;" />
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (E) -->
            <column name="bunrui5_kanji_na" mapping="BUNRUI5_KANJI_NA" label="蟆丞��鬘槫錐遘ｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>Bunrui5SearchQuery</id>
            <body>
                SELECT DISTINCT
                      RST.BUNRUI5_CD
                    , RB5.BUNRUI5_KANJI_NA
                FROM
                    MST_VW_SYOHIN_TAIKEI RST
                INNER JOIN (
                    SELECT
                          R.BUNRUI5_CD
                        , R.BUNRUI5_KANJI_NA
                        , R.YUKO_DT
                        , R.YUKO_END_DT
                    FROM
                        MST_R_BUNRUI5 R
                    WHERE
                        R.DELETE_FG = '0'
                ) RB5
                ON
                    RST.BUNRUI5_CD = RB5.BUNRUI5_CD
            </body>
            <where order="1" bind="param0">RST.SYSTEM_KB=:param0</where>
            <where order="2" bind="param1">:param1 BETWEEN RB5.YUKO_DT AND RB5.YUKO_END_DT</where>
            <where order="3" bind="param2">RST.BUNRUI1_CD=:param2</where>
            <where order="4" bind="param3">RST.BUNRUI2_CD=:param3</where>
            <where order="5" bind="param4">RST.BUNRUI3_CD=:param4</where>
            <where order="6" bind="bunrui5_nm">RB5.BUNRUI5_KANJI_NA LIKE %:bunrui5_nm%</where>
            <orderby>RST.BUNRUI5_CD ASC</orderby>
        </sql>
    </popup>
    <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (E) -->
    <popup id="AUT90921" title="蝠�蜩∵､懃ｴ｢" width="800" height="725" init="0" page="100" description="蝠�蜩∵､懃ｴ｢(蛻�鬘�1謖�螳�)">
        <panel width="520" cols="2">
            <input id="param0" type="hidden" value="param0" order="1" source=""></input>
            <message id="" value="驛ｨ髢繧ｳ繝ｼ繝�" order="2" style="margin:5px 35px 0px 0px;float:left;" />
            <input id="param1" type="text" size="10" maxlength="2" order="3" value="param1" style="ime-mode:disabled;float:left;" script="onblur='bunruiOnblur(this,1)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="2" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ2譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="螟ｧ蛻�鬘槭さ繝ｼ繝�" order="4" style="margin:5px 5px 0px 90px;float:left;" />
            <input id="param2" type="text" size="10" maxlength="4" order="5" value="param2" style="ime-mode:disabled;" script="onblur='bunruiOnblur(this,2)'">'
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="荳ｭ蛻�鬘槭さ繝ｼ繝�" order="6" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="param3" type="text" size="10" maxlength="6" order="7" value="param3" style="ime-mode:disabled;float:left;" script="onblur='bunruiOnblur(this,3)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="6" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ6譁�蟄励〒縺吶�" />
            </input>
            <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (S) -->
            <message id="" value="蟆丞��鬘槭さ繝ｼ繝�" order="8" style="margin:5px 7px 0px 88px;float:left;" />
            <input id="param4" type="text" size="10" maxlength="9" order="9" value="param4" style="ime-mode:disabled;" script="onblur='bunruiOnblur(this,4)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="9" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ9譁�蟄励〒縺吶�" />
            </input>
            <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (E) -->
            <message order="10" id="" value="蝠�蜩√さ繝ｼ繝�" style="margin:5px 35px 0px 0px;float:left;" />
            <input order="11" id="syohin_cd" value="syohin_cd" type="text" size="17" maxlength="13" style="ime-mode:disabled;float:left;" script="onblur='syohinOnblur(this);'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="13" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ13譁�蟄励〒縺吶�" />
            </input>
            <message order="12" id="" value="蝠�蜩∝錐遘ｰ" style="margin:5px 25px 0px 55px;float:left;" />
            <input   order="13" id="syohin_nm" value="syohin_nm" type="text" size="20" maxlength="20">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message order="14" id="notice1" value="蝠�蜩√さ繝ｼ繝峨∪縺溘�ｯ蝠�蜩∝錐遘ｰ縺ｮ荳驛ｨ繧貞�･蜉帙＠縺ｦ讀懃ｴ｢繝懊ち繝ｳ繧呈款縺励※縺上□縺輔＞縲�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="15" id="notice2" value="蝠�蜩√さ繝ｼ繝峨∝膚蜩∝錐遘ｰ繧貞�･蜉帙＠縺溷�ｴ蜷医�ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="16" id="notice3" value="蝠�蜩∵焚縺悟､壹＞蝣ｴ蜷医∵､懃ｴ｢蜿翫�ｳ謾ｹ繝壹�ｼ繧ｸ縺ｫ譎る俣縺後°縺九ｋ縺薙→縺後≠繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <input   order="17" id="search_button" value="讀懃ｴ｢" type="button" script="doSearch()" style="margin:6px 5px 5px 5px;text-align:center;"></input>
        </panel>
        <list width="450" cols="3">
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (S) -->
            <!-- <column order="1" name="syohin_cd" mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="height:28;text-align:center;" /> -->
            <column order="1" name="syohin_cd" mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="height:28;text-align:left;" />
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (S) -->
            <column order="2" name="hinmei_kanji_na" mapping="HINMEI_KANJI_NA" label="蝠�蜩∝錐遘ｰ" width="55%" style="text-align:left;padding-left:5px;" />
            <column order="3" name="Select" label="" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>SyohinSearchQuery</id>
            <body>
                SELECT
                      RS.SYOHIN_CD
                    , TRIM(CONCAT(RS.HINMEI_KANJI_NA, '縲', RS.KIKAKU_KANJI_NA)) HINMEI_KANJI_NA
                FROM (
                    SELECT
                          RS.SYOHIN_CD
                        , RS.YUKO_DT
                        , RS.YUKO_END_DT
                        , RS.HINMEI_KANJI_NA
                        , RS.HINMEI_KANA_NA
                        , ISNULL(RS.KIKAKU_KANJI_NA, RS.KIKAKU_KANA_NA) AS KIKAKU_KANJI_NA
                        , RS.BUNRUI1_CD
                        , RS.BUNRUI2_CD
                        <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (S) -->
                        , RS.BUNRUI3_CD
                        <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (E) -->
                        , RS.BUNRUI5_CD
                    FROM
                          MST_R_SYOHIN RS
                    WHERE
                          DELETE_FG = '0'
                  ) RS
            </body>
            <where order="1" bind="param0">:param0 BETWEEN RS.YUKO_DT AND RS.YUKO_END_DT</where>
            <where order="2" bind="param1">BUNRUI1_CD=:param1</where>
            <where order="3" bind="param2">BUNRUI2_CD=:param2</where>
            <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (S) -->
            <where order="4" bind="param3">BUNRUI3_CD=:param3</where>
            <where order="5" bind="param4">BUNRUI5_CD=:param4</where>
            <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (E) -->
            <where order="6" bind="syohin_cd">SYOHIN_CD LIKE %:syohin_cd%</where>
            <where order="7" bind="syohin_nm">
                (
                    HINMEI_KANJI_NA LIKE %:syohin_nm%
                    OR HINMEI_KANA_NA LIKE %:syohin_nm%
                    OR KIKAKU_KANJI_NA LIKE %:syohin_nm%
                )
            </where>
            <orderby>SYOHIN_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90925" title="繧ｻ繝ｳ繧ｿ繝ｼ讀懃ｴ｢" width="800" height="600" init="1" page="0"
        description="繧ｻ繝ｳ繧ｿ繝ｼ讀懃ｴ｢">
        <panel width="520" cols="1">
            <input id="param0" type="text" order="1" value="param0" style="display:none;"></input>
            <input id="param1" type="text" order="2" value="param1" style="display:none;"></input>
            <message id="" value="繧ｻ繝ｳ繧ｿ繝ｼ繧ｳ繝ｼ繝�" order="2" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="store_cd" type="text" size="12" maxlength="4" order="3" value="store_cd"
                style="ime-mode:disabled;margin:0px 50px 0px 0px;float:left;">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="繧ｻ繝ｳ繧ｿ繝ｼ蜷咲ｧｰ" order="4" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="store_nm" type="text" size="20" maxlength="20" order="5" value="store_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="6" value="繧ｻ繝ｳ繧ｿ繝ｼ繧ｳ繝ｼ繝峨√そ繝ｳ繧ｿ繝ｼ蜷咲ｧｰ縺ｨ繧ゅ↓縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="7" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="tenpo_cd" mapping="TENPO_CD" label="繧ｻ繝ｳ繧ｿ繝ｼ繧ｳ繝ｼ繝�" width="30%" order="1"
                style="height:28;text-align:left;" />
            <column name="tenpo_kanji_na" mapping="TENPO_KANJI_NA" label="繧ｻ繝ｳ繧ｿ繝ｼ蜷咲ｧｰ" width="55%" order="2"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" width="15%" order="3" style="text-align:center;" />
        </list>
        <sql>
            <id>TenpoSearchQuery</id>
            <body>SELECT TENPO_CD AS TENPO_CD, KANJI_NA AS TENPO_KANJI_NA FROM MST_R_TENPO</body>
            <where order="1" bind="param0">:param0 BETWEEN YUKO_DT AND YUKO_END_DT</where>
            <where order="2" bind="param1">TENPO_KB=:param1</where>
            <where order="3" bind="store_cd">TENPO_CD LIKE %:store_cd%</where>
            <where order="4" bind="store_nm">KANJI_NA LIKE %:store_nm%</where>
            <where order="5" bind="">DELETE_FG = '0'</where>
            <orderby>TENPO_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90922" title="蠎励げ繝ｫ繝ｼ繝玲､懃ｴ｢" width="800" height="600" init="1" page="0"
        description="蠎励げ繝ｫ繝ｼ繝玲､懃ｴ｢">
        <input id="param0" type="hidden" value="param0" order="1" source=""></input>
        <list width="500" cols="3">
            <column name="groupno_cd" mapping="GROUPNO_CD" label="蠎励げ繝ｫ繝ｼ繝励さ繝ｼ繝�" order="1" width="30%"
                style="height:28;text-align:left;" />
            <column name="name_na" mapping="TEN_GROUP_NA" label="蠎励げ繝ｫ繝ｼ繝怜錐遘ｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>TengroupSearchQuery</id>
            <body>
                SELECT
                      CONCAT('G', GROUPNO_CD) AS GROUPNO_CD
                    , NAME_NA AS TEN_GROUP_NA
                FROM
                    MST_R_TENGROUP_HEADER
            </body>
            <where order="1" bind="param0">YOTO_KB=:param0</where>
            <where order="2" bind="">DELETE_FG='0'</where>
            <orderby>GROUPNO_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90923" title="蜿門ｼ募�域､懃ｴ｢" width="800" height="600" init="1" page="50"
        description="蜿門ｼ募�域､懃ｴ｢">
        <panel width="560" cols="2">
            <message id="" value="蜿門ｼ募�医さ繝ｼ繝�" order="1" style="margin:5px 31px 0px 0px;float:left;" />
            <input id="torihikisaki_cd" type="text" size="10" maxlength="6" order="2" value="torihikisaki_cd"
            style="ime-mode:disabled;float:left;">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="6" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ6譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="蜿門ｼ募�亥錐遘ｰ" order="3" style="margin:5px 20px 0px 100px;float:left;" />
            <input id="torihikisaki_nm" type="text" size="20" maxlength="20" order="4" value="torihikisaki_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="6" value="蜿門ｼ募�医さ繝ｼ繝峨∝叙蠑募�亥錐遘ｰ縺ｨ繧ゅ↓縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="7" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="torihikisaki_cd" mapping="SHIIRESAKI_CD" label="蜿門ｼ募�医さ繝ｼ繝�" order="1" width="30%"
                style="height:28;text-align:left;" />
            <column name="tohihikisaki_kanji_na" mapping="SHIIRESAKI_KANJI_NA" label="蜿門ｼ募�亥錐遘ｰ" order="2" width="55%"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" order="3" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>TorihikisakiSearchQuery</id>
            <body>
                SELECT
                      SHIIRESAKI_CD AS SHIIRESAKI_CD
                    , SHIIRESAKI_KANJI_NA AS SHIIRESAKI_KANJI_NA
                FROM (
                    SELECT
                          TORIHIKISAKI_CD AS SHIIRESAKI_CD
                        , TORIHIKISAKI_KANJI_NA AS SHIIRESAKI_KANJI_NA
                    FROM
                        MST_R_TORIHIKISAKI RT
                    WHERE
                        RT.CHOAI_KB = '1'
                    AND RT.TORIKESHI_FG = '0'
                    AND RT.TEKIYO_START_DT = (
                        SELECT
                            MAX(SUB.TEKIYO_START_DT)
                        FROM
                            MST_R_TORIHIKISAKI SUB
                        WHERE
                            SUB.COMP_CD = RT.COMP_CD
                            AND SUB.CHOAI_KB = RT.CHOAI_KB
                            AND SUB.TORIHIKISAKI_CD = RT.TORIHIKISAKI_CD
                            AND SUB.TORIKESHI_FG = '0'
                            AND SUB.TORIHIKI_TEISHI_KB = '0'
                    )
                ) RT
            </body>
            <where order="1" bind="torihikisaki_cd">SHIIRESAKI_CD LIKE %:torihikisaki_cd%</where>
            <where order="2" bind="torihikisaki_nm">SHIIRESAKI_KANJI_NA LIKE %:torihikisaki_nm%</where>
            <orderby>SHIIRESAKI_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90924" title="繧ｻ繝ｳ繧ｿ繝ｼ讀懃ｴ｢" width="800" height="600" init="1" page="0"
        description="繧ｻ繝ｳ繧ｿ繝ｼ讀懃ｴ｢">
        <panel width="520" cols="1">
            <input id="param0" type="text" order="1" value="param0" style="display:none;"></input>
            <input id="param1" type="text" order="2" value="param1" style="display:none;"></input>
            <message id="" value="繧ｻ繝ｳ繧ｿ繝ｼ繧ｳ繝ｼ繝�" order="2" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="store_cd" type="text" size="12" maxlength="4" order="3" value="store_cd"
                style="ime-mode:disabled;margin:0px 50px 0px 0px;float:left;">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="繧ｻ繝ｳ繧ｿ繝ｼ蜷咲ｧｰ" order="4" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="store_nm" type="text" size="20" maxlength="20" order="5" value="store_nm">
                <validate id="maxLength" value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message id="notice" order="6" value="繧ｻ繝ｳ繧ｿ繝ｼ繧ｳ繝ｼ繝峨√そ繝ｳ繧ｿ繝ｼ蜷咲ｧｰ縺ｨ繧ゅ↓縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�"
                style="margin:5px 5px 5px 0px;text-align:left;" />
            <input id="search_button" type="button" order="7" value="讀懃ｴ｢" script="doSearch()"
                style="margin:6px 5px 5px 5px;text-align:center;">
            </input>
        </panel>
        <list width="500" cols="3">
            <column name="tenpo_cd" mapping="TENPO_CD" label="繧ｻ繝ｳ繧ｿ繝ｼ繧ｳ繝ｼ繝�" width="30%" order="1"
                style="height:28;text-align:left;" />
            <column name="tenpo_kanji_na" mapping="TENPO_KANJI_NA" label="繧ｻ繝ｳ繧ｿ繝ｼ蜷咲ｧｰ" width="55%" order="2"
                style="text-align:left;padding-left:5px;" />
            <column name="Select" label="" width="15%" order="3" style="text-align:center;" />
        </list>
        <sql>
            <id>TenpoSearchQuery</id>
            <body>SELECT TENPO_CD AS TENPO_CD, KANJI_NA AS TENPO_KANJI_NA FROM MST_R_TENPO</body>
            <where order="1" bind="param0">:param0 BETWEEN YUKO_DT AND YUKO_END_DT</where>
            <where order="2" bind="param1">TENPO_KB=:param1</where>
            <where order="3" bind="store_cd">TENPO_CD LIKE %:store_cd%</where>
            <where order="4" bind="store_nm">KANJI_NA LIKE %:store_nm%</where>
            <where order="5" bind="">DELETE_FG = '0'</where>
            <orderby>TENPO_CD ASC</orderby>
        </sql>
    </popup>
    <popup id="AUT90908" title="蝠�蜩∵､懃ｴ｢" width="800" height="725" init="0" page="100" description="蝠�蜩∵､懃ｴ｢(蛻�鬘�1謖�螳�)">
        <panel width="520" cols="2">
            <message id="" value="驛ｨ髢繧ｳ繝ｼ繝�" order="1" style="margin:5px 35px 0px 0px;float:left;"/>
            <input id="param0" type="text" size="10" maxlength="2" order="2" value="param0"
                style="ime-mode:disabled;float:left;" script="onblur='bunruiOnblur(this,1)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="2" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ2譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="螟ｧ蛻�鬘槭さ繝ｼ繝�" order="3" style="margin:5px 5px 0px 90px;float:left;" />
            <input id="param1" type="text" size="10" maxlength="4" order="4" value="param1" style="ime-mode:disabled;" script="onblur='bunruiOnblur(this,2)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="4" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ4譁�蟄励〒縺吶�" />
            </input>
            <message id="" value="荳ｭ蛻�鬘槭さ繝ｼ繝�" order="5" style="margin:5px 20px 0px 0px;float:left;" />
            <input id="param2" type="text" size="10" maxlength="6" order="6" value="param2" style="ime-mode:disabled;float:left;" script="onblur='bunruiOnblur(this,3)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="6" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ6譁�蟄励〒縺吶�" />
            </input>
            <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (S) -->
            <message id="" value="蟆丞��鬘槭さ繝ｼ繝�" order="7" style="margin:5px 7px 0px 88px;float:left;" />
            <input id="param3" type="text" size="10" maxlength="9" order="8" value="param3" style="ime-mode:disabled;" script="onblur='bunruiOnblur(this,4)'">
                <validate id="isHalfCharacter" value="" alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength" value="9" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ9譁�蟄励〒縺吶�" />
            </input>
            <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (E) -->
            <message order="9"  id=""              value="蝠�蜩√さ繝ｼ繝�" style="margin:5px 35px 0px 0px;float:left;" />
            <input   order="10" id="syohin_cd"     value="syohin_cd" type="text" size="17" maxlength="13" style="ime-mode:disabled;float:left;" script="onblur='syohinOnblur(this);'">
                <validate id="isHalfCharacter" value=""   alert="闍ｱ譁�蟄励∪縺溘�ｯ謨ｰ蟄励�ｮ縺ｿ蜈･蜉帙〒縺阪∪縺吶�" />
                <validate id="maxLength"       value="13" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ13譁�蟄励〒縺吶�" />
            </input>
            <message order="11"  id=""              value="蝠�蜩∝錐遘ｰ" style="margin:5px 25px 0px 55px;float:left;" />
            <input   order="12"  id="syohin_nm"     value="syohin_nm" type="text" size="20" maxlength="20">
                <validate id="maxLength"       value="20" alert="譛螟ｧ蜈･蜉帶枚蟄玲焚縺ｯ20譁�蟄励〒縺吶�" />
            </input>
            <message order="13"  id="notice1"       value="蝠�蜩√さ繝ｼ繝峨∪縺溘�ｯ蝠�蜩∝錐遘ｰ縺ｮ荳驛ｨ繧貞�･蜉帙＠縺ｦ讀懃ｴ｢繝懊ち繝ｳ繧呈款縺励※縺上□縺輔＞縲�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="14"  id="notice2"       value="蝠�蜩√さ繝ｼ繝峨∝膚蜩∝錐遘ｰ繧貞�･蜉帙＠縺溷�ｴ蜷医�ｯ縺ゅ＞縺ｾ縺�讀懃ｴ｢縺ｨ縺ｪ繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <message order="15"  id="notice3"       value="蝠�蜩∵焚縺悟､壹＞蝣ｴ蜷医∵､懃ｴ｢蜿翫�ｳ謾ｹ繝壹�ｼ繧ｸ縺ｫ譎る俣縺後°縺九ｋ縺薙→縺後≠繧翫∪縺吶�" style="margin:5px 5px 5px 0px;text-align:left;" />
            <input   order="16"  id="search_button" value="讀懃ｴ｢" type="button" script="doSearch()" style="margin:6px 5px 5px 5px;text-align:center;"></input>
            <!-- 2021/06/10 T.Kaita Redmine#3016蟇ｾ蠢� (S) -->
            <input   order="17"  id="param4"        value="param4" style="display: none;" />
            <!-- 2021/06/10 T.Kaita Redmine#3016蟇ｾ蠢� (E) -->
        </panel>
        <list width="450" cols="3">
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (S) -->
            <!--<column order="1" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="height:28;text-align:center;" /> -->
            <column order="1" name="syohin_cd"       mapping="SYOHIN_CD" label="蝠�蜩√さ繝ｼ繝�" width="30%" style="height:28;text-align:left;" />
            <!-- 2021/07/15 K.Nishikawa Redmine#3660蟇ｾ蠢� (E) -->
            <column order="2" name="hinmei_kanji_na" mapping="HINMEI_KANJI_NA" label="蝠�蜩∝錐遘ｰ" width="55%" style="text-align:left;padding-left:5px;" />
            <column order="3" name="Select"          label="" width="15%" style="text-align:center;" />
        </list>
        <sql>
            <id>SyohinSearchQuery</id>
              <body>SELECT SYOHIN_CD, TRIM(CONCAT(KIKAKU_KANJI_2_NA, ' ', SYOHIN_KANJI_NA, ' ', KIKAKU_KANJI_NA)) HINMEI_KANJI_NA FROM R_SYOHIN</body>
              <where order="1" bind="param0">TRIM(BUNRUI1_CD)=:param0</where>
              <where order="2" bind="param1">TRIM(BUNRUI2_CD)=:param1</where>
              <where order="3" bind="param2">TRIM(BUNRUI3_CD)=:param2</where>
              <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (S) -->
              <where order="4" bind="param3">TRIM(BUNRUI4_CD)=:param3</where>
              <!-- 2021/05/25 T.Kaita Redmine#2221�ｼ亥��鬘橸ｼ秘嚴螻､蛹厄ｼ牙ｯｾ蠢� (E) -->
              <where order="5" bind="syohin_cd">SYOHIN_CD LIKE %:syohin_cd%</where>
              <where order="6" bind="syohin_nm">SYOHIN_KANJI_NA LIKE %:syohin_nm%</where>
              <!-- 2021/06/10 T.Kaita Redmine#3016蟇ｾ蠢� (S) -->
              <where order="7" bind="param4">SHIIRE_HANBAI_KB=:param4</where>
              <!-- 2021/06/10 T.Kaita Redmine#3016蟇ｾ蠢� (E) -->
            <orderby>SYOHIN_CD ASC</orderby>
        </sql>
    </popup>
</config>